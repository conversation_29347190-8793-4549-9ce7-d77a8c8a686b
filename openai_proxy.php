<?php
// CORS 설정
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, OPTIONS');
header('Access-Control-Allow-Headers: Authorization, Content-Type');

// Preflight 요청 처리
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['error' => 'Method not allowed']);
    exit();
}

// OpenAI API 키 (배포 시에는 환경변수로 관리 권장)
$apiKey = getenv('OPENAI_API_KEY') ?: 'YOUR_OPENAI_API_KEY';
if ($apiKey === 'YOUR_OPENAI_API_KEY') {
    http_response_code(500);
    echo json_encode(['error' => 'API key not set']);
    exit();
}

// 이미지 파일 필수 확인
if (!isset($_FILES['image'])) {
    http_response_code(400);
    echo json_encode(['error' => 'Image file is required']);
    exit();
}

// CURLFile 객체 생성
$tmpName = $_FILES['image']['tmp_name'];
$mimeType = $_FILES['image']['type'] ?? 'image/png';
$fileName = $_FILES['image']['name'] ?? 'image.png';
$imageFile = new CURLFile($tmpName, $mimeType, $fileName);

// 기타 필드 수집 (prompt, model 등)
$postFields = [
    'image' => $imageFile,
    'prompt' => $_POST['prompt'] ?? '',
    'model'  => $_POST['model']  ?? 'gpt-image-1',
    'n'      => $_POST['n']      ?? '1',
    'size'   => $_POST['size']   ?? '1024x1024'
];

$ch = curl_init('https://api.openai.com/v1/images/edits');
curl_setopt($ch, CURLOPT_POST, true);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_HTTPHEADER, [
    'Authorization: Bearer ' . $apiKey
]);
curl_setopt($ch, CURLOPT_SAFE_UPLOAD, true);
curl_setopt($ch, CURLOPT_POSTFIELDS, $postFields);

$response = curl_exec($ch);
$status   = curl_getinfo($ch, CURLINFO_HTTP_CODE);
$error    = curl_error($ch);
curl_close($ch);

http_response_code($status ?: 500);
if ($response === false) {
    echo json_encode(['error' => 'Curl error', 'message' => $error]);
} else {
    header('Content-Type: application/json');
    echo $response;
}
?>