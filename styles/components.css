/* Microsoft Fluent 2 Web 컴포넌트 스타일 */

/* 날짜 선택 섹션 */
.date-selector {
  margin-bottom: var(--fluent-space-vertical-xxxl);
}

.date-selector h2 {
  font-family: var(--fluent-font-family-base);
  font-size: var(--fluent-font-size-600);
  font-weight: var(--fluent-font-weight-semibold);
  line-height: var(--fluent-line-height-600);
  color: var(--fluent-neutral-foreground-rest);
  margin-bottom: var(--fluent-space-vertical-l);
  display: flex;
  align-items: center;
  gap: var(--fluent-space-horizontal-s);
}

.date-input-group {
  display: flex;
  flex-direction: column;
  gap: var(--fluent-space-vertical-m);
  margin-bottom: var(--fluent-space-vertical-l);
}

.date-input {
  font-family: var(--fluent-font-family-base);
  font-size: var(--fluent-font-size-400);
  font-weight: var(--fluent-font-weight-regular);
  line-height: var(--fluent-line-height-400);
  color: var(--fluent-neutral-foreground-rest);
  background-color: var(--fluent-neutral-background-1);
  border: 2px solid var(--fluent-neutral-stroke-1);
  border-radius: var(--fluent-border-radius-large);
  padding: var(--fluent-space-vertical-m) var(--fluent-space-horizontal-l);
  width: 100%;
  max-width: 300px;
  transition: all var(--fluent-duration-normal) var(--fluent-curve-easy-ease);
  outline: none;
  position: relative;
  box-shadow: var(--fluent-shadow-2);
  color-scheme: light;
}

/* 캘린더 아이콘 색상 변경 */
.date-input::-webkit-calendar-picker-indicator {
  color: rgb(71, 158, 245);
  filter: invert(42%) sepia(93%) saturate(1352%) hue-rotate(204deg) brightness(103%) contrast(101%);
  cursor: pointer;
}

.date-input:hover:not(:disabled) {
  border-color: var(--fluent-brand-background-1);
  box-shadow: var(--fluent-shadow-4);
  transform: translateY(-1px);
}

.date-input:focus {
  border-color: var(--fluent-brand-background-1);
  box-shadow: 0 0 0 3px rgba(0, 120, 212, 0.1), var(--fluent-shadow-8);
  transform: translateY(-2px);
}

.date-input:disabled {
  background-color: var(--fluent-neutral-background-3);
  color: var(--fluent-neutral-foreground-disabled);
  border-color: var(--fluent-neutral-stroke-1);
  cursor: not-allowed;
  opacity: 0.6;
  transform: none;
  box-shadow: none;
}

.unknown-date-checkbox {
  display: flex;
  align-items: center;
  gap: var(--fluent-space-horizontal-s);
  padding: var(--fluent-space-vertical-s) var(--fluent-space-horizontal-m);
  background-color: var(--fluent-subtle-background);
  border: 1px solid var(--fluent-neutral-stroke-1);
  border-radius: var(--fluent-border-radius-medium);
  transition: all var(--fluent-duration-fast) var(--fluent-curve-easy-ease);
  cursor: pointer;
  user-select: none;
}

.unknown-date-checkbox:hover {
  background-color: var(--fluent-subtle-background-hover);
  border-color: var(--fluent-brand-stroke-1);
}

.unknown-date-checkbox input[type="checkbox"] {
  width: 18px;
  height: 18px;
  margin: 0;
  cursor: pointer;
  accent-color: var(--fluent-brand-background-1);
}

.unknown-date-checkbox label {
  font-family: var(--fluent-font-family-base);
  font-size: var(--fluent-font-size-300);
  font-weight: var(--fluent-font-weight-medium);
  line-height: var(--fluent-line-height-300);
  color: var(--fluent-neutral-foreground-rest);
  cursor: pointer;
  margin: 0;
  display: flex;
  align-items: center;
  gap: var(--fluent-space-horizontal-xs);
}

.date-preview {
  margin-top: var(--fluent-space-vertical-m);
  padding: var(--fluent-space-vertical-m) var(--fluent-space-horizontal-l);
  background-color: var(--fluent-neutral-background-2);
  border: 1px solid var(--fluent-neutral-stroke-1);
  border-radius: var(--fluent-border-radius-medium);
  border-left: 4px solid var(--fluent-brand-background-1);
  transition: all var(--fluent-duration-fast) var(--fluent-curve-easy-ease);
}

.date-preview p {
  font-family: var(--fluent-font-family-base);
  font-size: var(--fluent-font-size-300);
  font-weight: var(--fluent-font-weight-medium);
  line-height: var(--fluent-line-height-300);
  margin: 0;
  display: flex;
  align-items: center;
  gap: var(--fluent-space-horizontal-xs);
}

.date-preview:empty {
  display: none;
}

/* 키워드 입력 섹션 */
.keyword-input {
  margin-bottom: var(--fluent-space-vertical-xxxl);
}

.keyword-input h2 {
  font-family: var(--fluent-font-family-base);
  font-size: var(--fluent-font-size-600);
  font-weight: var(--fluent-font-weight-semibold);
  line-height: var(--fluent-line-height-600);
  color: var(--fluent-neutral-foreground-rest);
  margin-bottom: var(--fluent-space-vertical-l);
}

/* 선택된 날짜 정보 카드 */
.date-info-card {
  background: linear-gradient(135deg, var(--fluent-brand-background-1) 0%, var(--fluent-brand-background-2) 100%);
  border: 1px solid var(--fluent-brand-stroke-1);
  border-radius: var(--fluent-border-radius-large);
  padding: var(--fluent-space-vertical-l) var(--fluent-space-horizontal-l);
  margin-bottom: var(--fluent-space-vertical-l);
  box-shadow: var(--fluent-shadow-4);
  transition: all var(--fluent-duration-normal) var(--fluent-curve-easy-ease);
}

.date-info-card:hover {
  box-shadow: var(--fluent-shadow-8);
  transform: translateY(-2px);
}

.date-info-card h3 {
  font-family: var(--fluent-font-family-base);
  font-size: var(--fluent-font-size-400);
  font-weight: var(--fluent-font-weight-semibold);
  line-height: var(--fluent-line-height-400);
  color: white;
  margin: 0 0 var(--fluent-space-vertical-s) 0;
  display: flex;
  align-items: center;
  gap: var(--fluent-space-horizontal-s);
}

.date-info-card .selected-date {
  font-family: var(--fluent-font-family-base);
  font-size: var(--fluent-font-size-500);
  font-weight: var(--fluent-font-weight-medium);
  line-height: var(--fluent-line-height-500);
  color: white;
  margin: 0;
  background-color: rgba(255, 255, 255, 0.1);
  padding: var(--fluent-space-vertical-xs) var(--fluent-space-horizontal-s);
  border-radius: var(--fluent-border-radius-medium);
  backdrop-filter: blur(10px);
 }
 
 /* Step 4 Data Display 스타일 */
.step4-content {
  padding: var(--fluent-space-vertical-l);
}

.session-data-container {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: var(--fluent-space-vertical-l);
  margin: var(--fluent-space-vertical-l) 0;
}

.data-card {
  background-color: var(--fluent-neutral-background-1);
  border: 1px solid var(--fluent-neutral-stroke-1);
  border-radius: var(--fluent-border-radius-large);
  padding: var(--fluent-space-vertical-l);
  box-shadow: var(--fluent-shadow-2);
  transition: all var(--fluent-duration-normal) var(--fluent-curve-easy-ease);
}

.data-card:hover {
  box-shadow: var(--fluent-shadow-8);
  transform: translateY(-2px);
  border-color: var(--fluent-brand-stroke-1);
}

.priority-card {
  border-left: 4px solid var(--fluent-brand-background-1);
  background: linear-gradient(135deg, var(--fluent-neutral-background-1) 0%, var(--fluent-subtle-background) 100%);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--fluent-space-vertical-m);
  padding-bottom: var(--fluent-space-vertical-s);
  border-bottom: 1px solid var(--fluent-neutral-stroke-1);
}

.card-header h4 {
  font-family: var(--fluent-font-family-base);
  font-size: var(--fluent-font-size-400);
  font-weight: var(--fluent-font-weight-semibold);
  color: var(--fluent-neutral-foreground-rest);
  margin: 0;
  display: flex;
  align-items: center;
  gap: var(--fluent-space-horizontal-s);
}

.key-name {
  font-family: var(--fluent-font-family-monospace);
  font-size: var(--fluent-font-size-200);
  color: var(--fluent-neutral-foreground-2);
  background-color: var(--fluent-neutral-background-3);
  padding: 2px 6px;
  border-radius: var(--fluent-border-radius-small);
}

.card-content {
  font-family: var(--fluent-font-family-base);
  font-size: var(--fluent-font-size-300);
  line-height: var(--fluent-line-height-300);
}

.simple-value {
  padding: var(--fluent-space-vertical-s);
  background-color: var(--fluent-neutral-background-2);
  border-radius: var(--fluent-border-radius-medium);
  font-family: var(--fluent-font-family-monospace);
  word-break: break-all;
}

.object-data, .array-data {
  color: var(--fluent-neutral-foreground-rest);
}

.object-item {
  padding: var(--fluent-space-vertical-xs) 0;
  border-bottom: 1px solid var(--fluent-neutral-stroke-2);
}

.object-item:last-child {
  border-bottom: none;
}

.object-item strong {
  color: var(--fluent-brand-foreground-1);
  font-weight: var(--fluent-font-weight-semibold);
}

.array-preview {
  margin-top: var(--fluent-space-vertical-s);
}

.array-item {
  padding: var(--fluent-space-vertical-xs);
  background-color: var(--fluent-neutral-background-2);
  border-radius: var(--fluent-border-radius-small);
  margin-bottom: var(--fluent-space-vertical-xs);
  font-family: var(--fluent-font-family-monospace);
  font-size: var(--fluent-font-size-200);
}

.more-items {
  color: var(--fluent-neutral-foreground-2);
  font-style: italic;
  padding: var(--fluent-space-vertical-xs) 0;
}

.empty-array {
  color: var(--fluent-neutral-foreground-2);
  font-style: italic;
  text-align: center;
  padding: var(--fluent-space-vertical-m);
}

.no-data-message {
  text-align: center;
  padding: var(--fluent-space-vertical-xxxl);
  color: var(--fluent-neutral-foreground-2);
  font-size: var(--fluent-font-size-400);
  background-color: var(--fluent-neutral-background-2);
  border: 1px dashed var(--fluent-neutral-stroke-1);
  border-radius: var(--fluent-border-radius-large);
}

.refresh-section {
  text-align: center;
  margin-top: var(--fluent-space-vertical-l);
  padding-top: var(--fluent-space-vertical-l);
  border-top: 1px solid var(--fluent-neutral-stroke-1);
}

/* Images Array Display Styles */
.images-array-container {
  background: var(--fluent-neutral-background-1);
  border: 1px solid var(--fluent-neutral-stroke-1);
  border-radius: var(--fluent-border-radius-large);
  padding: var(--fluent-space-vertical-l);
  margin-top: var(--fluent-space-vertical-m);
}

.images-header {
  font-family: var(--fluent-font-family-base);
  font-size: var(--fluent-font-size-500);
  font-weight: var(--fluent-font-weight-semibold);
  color: var(--fluent-neutral-foreground-rest);
  margin-bottom: var(--fluent-space-vertical-l);
  padding-bottom: var(--fluent-space-vertical-s);
  border-bottom: 2px solid var(--fluent-brand-background-1);
}

.image-item {
  display: flex;
  gap: var(--fluent-space-horizontal-l);
  padding: var(--fluent-space-vertical-l);
  background: var(--fluent-neutral-background-2);
  border: 1px solid var(--fluent-neutral-stroke-1);
  border-radius: var(--fluent-border-radius-medium);
  margin-bottom: var(--fluent-space-vertical-m);
  transition: all var(--fluent-duration-normal) var(--fluent-curve-easy-ease);
}

.image-item:hover {
  border-color: var(--fluent-brand-stroke-1);
  box-shadow: var(--fluent-shadow-4);
  transform: translateY(-1px);
}

.image-item:last-child {
  margin-bottom: 0;
}

.image-thumbnail {
  flex-shrink: 0;
  width: 120px;
  height: 120px;
  border-radius: var(--fluent-border-radius-medium);
  overflow: hidden;
  border: 1px solid var(--fluent-neutral-stroke-1);
  background: var(--fluent-neutral-background-3);
}

.image-thumbnail img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform var(--fluent-duration-normal) var(--fluent-curve-easy-ease);
}

.image-thumbnail:hover img {
  transform: scale(1.05);
}

.image-thumbnail-200 {
  flex-shrink: 0;
  border-radius: 8px;
  overflow: hidden;
  border: 1px solid var(--colorNeutralStroke2);
  background: var(--colorNeutralBackground3);
  margin-bottom: 12px;
}

.image-thumbnail-200 img {
  display: block;
  border-radius: 8px;
  transition: transform 0.2s ease;
}

.image-thumbnail-200:hover img {
  transform: scale(1.02);
}

.image-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: var(--fluent-space-vertical-s);
}

.image-name {
  font-family: var(--fluent-font-family-base);
  font-size: var(--fluent-font-size-300);
  font-weight: var(--fluent-font-weight-semibold);
  color: var(--fluent-neutral-foreground-rest);
  word-break: break-word;
}

.image-filename {
  font-family: var(--fluent-font-family-base);
  font-size: var(--fluent-font-size-300);
  font-weight: var(--fluent-font-weight-medium);
  color: var(--fluent-neutral-foreground-rest);
  margin-bottom: var(--fluent-space-vertical-xs);
  word-break: break-word;
}

.image-caption {
  font-family: var(--fluent-font-family-base);
  font-size: var(--fluent-font-size-300);
  line-height: var(--fluent-line-height-400);
  color: var(--fluent-neutral-foreground-rest);
  background: var(--fluent-subtle-background);
  padding: var(--fluent-space-vertical-m);
  border-radius: var(--fluent-border-radius-medium);
  border-left: 3px solid var(--fluent-brand-background-1);
  margin-bottom: var(--fluent-space-vertical-xs);
}

.image-base64-info {
  font-family: var(--fluent-font-family-base);
  font-size: var(--fluent-font-size-200);
  color: var(--fluent-neutral-foreground-2);
  margin-bottom: var(--fluent-space-vertical-xs);
}

/* Action Buttons Container */
.action-buttons-container {
  margin-top: 30px;
  padding: 20px 0;
  border-top: 2px solid #e9ecef;
}

.action-buttons {
  display: flex;
  gap: 15px;
  justify-content: center;
  align-items: center;
  flex-wrap: wrap;
}

.action-btn {
  padding: 12px 24px;
  border: none;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  min-width: 180px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.action-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.action-btn:active {
  transform: translateY(0);
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
}

.primary-btn {
  background: linear-gradient(135deg, #007bff, #0056b3);
  color: white;
}

.primary-btn:hover {
  background: linear-gradient(135deg, #0056b3, #004085);
}

.secondary-btn {
  background: linear-gradient(135deg, #28a745, #1e7e34);
  color: white;
}

.secondary-btn:hover {
  background: linear-gradient(135deg, #1e7e34, #155724);
}

@media (max-width: 768px) {
  .action-buttons {
    flex-direction: column;
    gap: 10px;
  }
  
  .action-btn {
    width: 100%;
    max-width: 300px;
  }
}

.image-status {
  display: flex;
  gap: var(--fluent-space-horizontal-s);
  flex-wrap: wrap;
  margin-top: auto;
  font-family: var(--fluent-font-family-base);
  font-size: var(--fluent-font-size-200);
  color: var(--fluent-neutral-foreground-2);
}

/* 이미지 분석 결과 스타일 */
.analysis-results {
  margin-top: var(--fluent-space-vertical-l);
  padding: var(--fluent-space-vertical-l) var(--fluent-space-horizontal-l);
  background-color: var(--fluent-neutral-background-2);
  border: 1px solid var(--fluent-neutral-stroke-1);
  border-radius: var(--fluent-border-radius-large);
 }

 /* 생성된 랜딩페이지 섹션 스타일 */
 .generated-sections {
   margin-top: var(--fluent-space-vertical-xl);
   padding: var(--fluent-space-vertical-l) var(--fluent-space-horizontal-l);
   background-color: var(--fluent-neutral-background-2);
   border: 1px solid var(--fluent-neutral-stroke-1);
   border-radius: var(--fluent-border-radius-large);
   border-left: 4px solid var(--fluent-brand-background-1);
   box-shadow: var(--fluent-shadow-4);
   transition: all var(--fluent-duration-normal) var(--fluent-curve-easy-ease);
   animation: slideInUp 0.5s ease-out;
 }

 .generated-sections:hover {
   box-shadow: var(--fluent-shadow-8);
   transform: translateY(-1px);
 }

 .generated-sections h3 {
   font-family: var(--fluent-font-family-base);
   font-size: var(--fluent-font-size-600);
   font-weight: var(--fluent-font-weight-semibold);
   color: var(--fluent-neutral-foreground-rest);
   margin: 0 0 var(--fluent-space-vertical-l) 0;
   text-align: center;
   display: flex;
   align-items: center;
   justify-content: center;
   gap: var(--fluent-space-horizontal-s);
 }

 .sections-grid {
   display: grid;
   grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
   gap: var(--fluent-space-vertical-l);
   margin-top: var(--fluent-space-vertical-l);
 }

 .section-card {
   background-color: white;
   border: 1px solid var(--fluent-neutral-stroke-1);
   border-radius: var(--fluent-border-radius-medium);
   padding: var(--fluent-space-vertical-l) var(--fluent-space-horizontal-l);
   box-shadow: var(--fluent-shadow-4);
   transition: all var(--fluent-duration-normal) var(--fluent-curve-easy-ease);
   position: relative;
   overflow: hidden;
 }

 .section-card::before {
   content: '';
   position: absolute;
   top: 0;
   left: 0;
   right: 0;
   height: 4px;
   background: linear-gradient(90deg, var(--fluent-brand-background-1), var(--fluent-brand-background-2));
 }

 .section-card:hover {
   box-shadow: var(--fluent-shadow-16);
   transform: translateY(-4px);
   border-color: var(--fluent-brand-stroke-1);
 }

 .section-card h4 {
   font-family: var(--fluent-font-family-base);
   font-size: var(--fluent-font-size-500);
   font-weight: var(--fluent-font-weight-semibold);
   color: var(--fluent-neutral-foreground-rest);
   margin: 0 0 var(--fluent-space-vertical-m) 0;
   display: flex;
   align-items: center;
   gap: var(--fluent-space-horizontal-s);
   padding-bottom: var(--fluent-space-vertical-s);
   border-bottom: 1px solid var(--fluent-neutral-stroke-2);
 }

 .section-content {
   font-family: var(--fluent-font-family-base);
   font-size: var(--fluent-font-size-300);
   line-height: var(--fluent-line-height-400);
   color: #000000;
   white-space: pre-wrap;
   word-wrap: break-word;
 }

 .section-content br {
   margin-bottom: var(--fluent-space-vertical-xs);
 }

 /* 애니메이션 */
 @keyframes slideInUp {
   from {
     opacity: 0;
     transform: translateY(30px);
   }
   to {
     opacity: 1;
     transform: translateY(0);
   }
 }

 /* 준비 완료 버튼 스타일 개선 */
 .ready-btn {
   background: linear-gradient(135deg, var(--fluent-brand-background-1) 0%, var(--fluent-brand-background-2) 100%);
   color: white;
   border: none;
   border-radius: var(--fluent-border-radius-medium);
   padding: var(--fluent-space-vertical-m) var(--fluent-space-horizontal-xl);
   font-family: var(--fluent-font-family-base);
   font-size: var(--fluent-font-size-400);
   font-weight: var(--fluent-font-weight-semibold);
   cursor: pointer;
   transition: all var(--fluent-duration-normal) var(--fluent-curve-easy-ease);
   box-shadow: var(--fluent-shadow-4);
   margin: var(--fluent-space-vertical-l) auto;
   display: block;
   min-width: 200px;
 }

 .ready-btn:hover:not(:disabled) {
   box-shadow: var(--fluent-shadow-16);
   transform: translateY(-2px);
 }

 .ready-btn:active {
   transform: translateY(0);
   box-shadow: var(--fluent-shadow-2);
 }

 .ready-btn:disabled {
   opacity: 0.6;
   cursor: not-allowed;
   transform: none;
   box-shadow: var(--fluent-shadow-2);
 }

 /* 반응형 디자인 */
  @media (max-width: 768px) {
    .sections-grid {
      grid-template-columns: 1fr;
      gap: var(--fluent-space-vertical-m);
    }
    
    .section-card {
      padding: var(--fluent-space-vertical-m) var(--fluent-space-horizontal-m);
    }
    
    .generated-sections {
      padding: var(--fluent-space-vertical-m) var(--fluent-space-horizontal-m);
    }
  }
 
 .analysis-results h3 {
   font-family: var(--fluent-font-family-base);
   font-size: var(--fluent-font-size-500);
   font-weight: var(--fluent-font-weight-semibold);
   color: var(--fluent-neutral-foreground-rest);
   margin: 0 0 var(--fluent-space-vertical-m) 0;
   display: flex;
   align-items: center;
   gap: var(--fluent-space-horizontal-s);
 }
 
 .analysis-item {
   background-color: var(--fluent-neutral-background-1);
   border: 1px solid var(--fluent-neutral-stroke-1);
   border-radius: var(--fluent-border-radius-medium);
   padding: var(--fluent-space-vertical-m) var(--fluent-space-horizontal-m);
   margin-bottom: var(--fluent-space-vertical-m);
   transition: all var(--fluent-duration-fast) var(--fluent-curve-easy-ease);
 }
 
 .analysis-item:hover {
   box-shadow: var(--fluent-shadow-4);
   transform: translateY(-1px);
 }
 
 .analysis-item h4 {
   font-family: var(--fluent-font-family-base);
   font-size: var(--fluent-font-size-400);
   font-weight: var(--fluent-font-weight-medium);
   color: var(--fluent-neutral-foreground-rest);
   margin: 0 0 var(--fluent-space-vertical-s) 0;
   display: flex;
   align-items: center;
   gap: var(--fluent-space-horizontal-s);
 }
 
 .analysis-content {
    font-family: var(--fluent-font-family-base);
    font-size: var(--fluent-font-size-300);
    line-height: var(--fluent-line-height-400);
    color: var(--fluent-neutral-foreground-rest);
    background-color: var(--fluent-subtle-background);
    padding: var(--fluent-space-vertical-s) var(--fluent-space-horizontal-m);
    border-radius: var(--fluent-border-radius-medium);
    border-left: 4px solid var(--fluent-brand-background-1);
    margin-bottom: var(--fluent-space-vertical-s);
    cursor: text;
    transition: all var(--fluent-duration-fast) var(--fluent-curve-easy-ease);
  }
  
  .analysis-content:hover {
    background-color: var(--fluent-neutral-background-1);
    border-left-color: var(--fluent-brand-background-2);
  }
  
  .analysis-content:focus {
    outline: 2px solid var(--fluent-brand-background-1);
    outline-offset: 2px;
    background-color: var(--fluent-neutral-background-1);
  }
 
 .analysis-error {
   font-family: var(--fluent-font-family-base);
   font-size: var(--fluent-font-size-300);
   color: var(--fluent-palette-red-foreground-1);
   background-color: var(--fluent-palette-red-background-1);
   padding: var(--fluent-space-vertical-s) var(--fluent-space-horizontal-m);
   border-radius: var(--fluent-border-radius-medium);
   border-left: 4px solid var(--fluent-palette-red-border-1);
 }
 

 

 
 /* 이전 데이터 정보 컨테이너 (step3용) */
 .previous-data-container {
   margin-bottom: var(--fluent-space-vertical-xl);
 }
 
 .previous-data-container h3 {
   font-family: var(--fluent-font-family-base);
   font-size: var(--fluent-font-size-500);
   font-weight: var(--fluent-font-weight-semibold);
   line-height: var(--fluent-line-height-500);
   color: var(--fluent-neutral-foreground-rest);
   margin: 0 0 var(--fluent-space-vertical-m) 0;
   display: flex;
   align-items: center;
   gap: var(--fluent-space-horizontal-s);
 }
 
 .info-cards-grid {
   display: grid;
   grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
   gap: var(--fluent-space-horizontal-m);
   margin-bottom: var(--fluent-space-vertical-l);
 }
 
 .info-card {
   background-color: var(--fluent-neutral-background-1);
   border: 1px solid var(--fluent-neutral-stroke-1);
   border-radius: var(--fluent-border-radius-large);
   padding: var(--fluent-space-vertical-m) var(--fluent-space-horizontal-m);
   box-shadow: var(--fluent-shadow-2);
   transition: all var(--fluent-duration-normal) var(--fluent-curve-easy-ease);
 }
 
 .info-card:hover {
   box-shadow: var(--fluent-shadow-4);
   transform: translateY(-1px);
 }

 /* Step 4 페이지 전용 h4 스타일 */
 .step4-page h4 {
   color: #000000 !important;
 }
 
 .info-card h4 {
   font-family: var(--fluent-font-family-base);
   font-size: var(--fluent-font-size-400);
   font-weight: var(--fluent-font-weight-semibold);
   line-height: var(--fluent-line-height-400);
   color: var(--fluent-neutral-foreground-rest);
   margin: 0 0 var(--fluent-space-vertical-s) 0;
   display: flex;
   align-items: center;
   gap: var(--fluent-space-horizontal-xs);
 }
 
 .info-card p {
   font-family: var(--fluent-font-family-base);
   font-size: var(--fluent-font-size-300);
   font-weight: var(--fluent-font-weight-regular);
   line-height: var(--fluent-line-height-300);
   color: var(--fluent-neutral-foreground-rest);
   margin: 0;
 }
 
 .date-card {
   border-left: 4px solid var(--fluent-brand-background-1);
 }
 
 .keyword-card {
   border-left: 4px solid var(--fluent-brand-background-2);
 }

 .image-card {
   border-left: 4px solid var(--fluent-palette-yellow-background-1);
 }
 
 .keyword-content {
   display: flex;
   flex-direction: column;
   gap: var(--fluent-space-vertical-xs);
 }

 .image-content {
   display: flex;
   flex-direction: column;
   gap: var(--fluent-space-vertical-xs);
 }
 
 .info-item {
   font-family: var(--fluent-font-family-base);
   font-size: var(--fluent-font-size-300);
   font-weight: var(--fluent-font-weight-regular);
   line-height: var(--fluent-line-height-300);
   color: var(--fluent-neutral-foreground-rest);
   padding: var(--fluent-space-vertical-xs) 0;
 }
 
 .info-item strong {
   font-weight: var(--fluent-font-weight-semibold);
   color: var(--fluent-brand-foreground-1);
 }
 
 /* 입력 그룹 */
.input-group {
  margin-bottom: var(--fluent-space-vertical-l);
}

.input-group label {
  font-family: var(--fluent-font-family-base);
  font-size: var(--fluent-font-size-300);
  font-weight: var(--fluent-font-weight-medium);
  line-height: var(--fluent-line-height-300);
  color: var(--fluent-neutral-foreground-rest);
  display: block;
  margin-bottom: var(--fluent-space-vertical-xs);
}

.input-group input,
.input-group textarea {
  font-family: var(--fluent-font-family-base);
  font-size: var(--fluent-font-size-300);
  font-weight: var(--fluent-font-weight-regular);
  line-height: var(--fluent-line-height-300);
  color: var(--fluent-neutral-foreground-rest);
  background-color: var(--fluent-neutral-background-1);
  border: 1px solid var(--fluent-neutral-stroke-1);
  border-radius: var(--fluent-border-radius-medium);
  padding: var(--fluent-space-vertical-s) var(--fluent-space-horizontal-m);
  width: 100%;
  transition: all var(--fluent-duration-fast) var(--fluent-curve-easy-ease);
  outline: none;
}

.input-group input:hover,
.input-group textarea:hover {
  border-color: var(--fluent-neutral-stroke-2);
}

.input-group input:focus,
.input-group textarea:focus {
  border-color: var(--fluent-brand-background-1);
  box-shadow: 0 0 0 1px var(--fluent-brand-background-1);
}

.input-group input:disabled,
.input-group textarea:disabled {
  background-color: var(--fluent-neutral-background-3);
  color: var(--fluent-neutral-foreground-disabled);
  border-color: var(--fluent-neutral-stroke-1);
  cursor: not-allowed;
}

.input-group textarea {
  min-height: 100px;
  resize: vertical;
}

/* 이미지 업로드 섹션 */
.image-upload {
  margin-bottom: var(--fluent-space-vertical-xxxl);
}

.image-upload h2 {
  font-family: var(--fluent-font-family-base);
  font-size: var(--fluent-font-size-600);
  font-weight: var(--fluent-font-weight-semibold);
  line-height: var(--fluent-line-height-600);
  color: var(--fluent-neutral-foreground-rest);
  margin-bottom: var(--fluent-space-vertical-l);
}

.upload-area {
  border: 2px dashed var(--fluent-neutral-stroke-1);
  border-radius: var(--fluent-border-radius-large);
  padding: var(--fluent-space-vertical-xxxl);
  text-align: center;
  background-color: var(--fluent-neutral-background-2);
  transition: all var(--fluent-duration-normal) var(--fluent-curve-easy-ease);
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

.upload-area:hover {
  border-color: var(--fluent-brand-background-1);
  background-color: var(--fluent-subtle-background);
}

.upload-area.dragover {
  border-color: var(--fluent-brand-background-1);
  background-color: var(--fluent-subtle-background-hover);
  transform: scale(1.02);
}

.upload-icon {
  font-size: var(--fluent-font-size-800);
  color: var(--fluent-neutral-stroke-accessible);
  margin-bottom: var(--fluent-space-vertical-l);
  display: block;
}

.upload-text {
  font-family: var(--fluent-font-family-base);
  font-size: var(--fluent-font-size-400);
  font-weight: var(--fluent-font-weight-regular);
  line-height: var(--fluent-line-height-400);
  color: var(--fluent-neutral-foreground-rest);
  margin-bottom: var(--fluent-space-vertical-s);
}

.upload-hint {
  font-family: var(--fluent-font-family-base);
  font-size: var(--fluent-font-size-200);
  font-weight: var(--fluent-font-weight-regular);
  line-height: var(--fluent-line-height-200);
  color: var(--fluent-neutral-foreground-disabled);
}

.file-input {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  opacity: 0;
  cursor: pointer;
}

/* 이미지 미리보기 */
.image-preview {
  margin-top: var(--fluent-space-vertical-l);
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
  gap: var(--fluent-space-horizontal-l);
}

/* 이미지 리스트 갤러리 */
.image-list {
  margin-top: var(--fluent-space-vertical-l);
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 450px));
  gap: var(--fluent-space-horizontal-m);
  justify-content: start;
}

.image-item {
  position: relative;
  border-radius: var(--fluent-border-radius-large);
  overflow: hidden;
  background-color: var(--fluent-neutral-background-1);
  border: 1px solid var(--fluent-neutral-stroke-1);
  box-shadow: var(--fluent-shadow-2);
  transition: all var(--fluent-duration-normal) var(--fluent-curve-easy-ease);
  width: 100%;
}

.image-item:hover {
  box-shadow: var(--fluent-shadow-8);
  transform: translateY(-2px);
  border-color: var(--fluent-brand-stroke-1);
}

.image-item img {
  width: 100%;
  height: 200px;
  object-fit: cover;
  display: block;
  border-radius: var(--fluent-border-radius-large) var(--fluent-border-radius-large) 0 0;
}

.image-info {
  padding: var(--fluent-space-vertical-s) var(--fluent-space-horizontal-m);
  background-color: var(--fluent-neutral-background-1);
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: var(--fluent-space-horizontal-s);
}

.image-name {
  font-family: var(--fluent-font-family-base);
  font-size: var(--fluent-font-size-200);
  font-weight: var(--fluent-font-weight-medium);
  line-height: var(--fluent-line-height-200);
  color: var(--fluent-neutral-foreground-rest);
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.image-remove {
  background-color: transparent;
  border: 1px solid var(--fluent-neutral-stroke-1);
  border-radius: var(--fluent-border-radius-circular);
  width: 28px;
  height: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  font-size: var(--fluent-font-size-200);
  color: var(--fluent-neutral-foreground-rest);
  transition: all var(--fluent-duration-fast) var(--fluent-curve-easy-ease);
  flex-shrink: 0;
}

.image-remove:hover {
  background-color: #dc3545;
  border-color: #dc3545;
  color: #ffffff;
  transform: scale(1.1);
}

/* 이미지 카운터 */
.image-counter {
  font-family: var(--fluent-font-family-base);
  font-size: var(--fluent-font-size-300);
  font-weight: var(--fluent-font-weight-medium);
  line-height: var(--fluent-line-height-300);
  color: var(--fluent-neutral-foreground-rest);
  background-color: var(--fluent-subtle-background);
  border: 1px solid var(--fluent-neutral-stroke-1);
  border-radius: var(--fluent-border-radius-medium);
  padding: var(--fluent-space-vertical-s) var(--fluent-space-horizontal-m);
  margin: var(--fluent-space-vertical-m) 0;
  display: inline-block;
  backdrop-filter: blur(10px);
}

.preview-item {
  position: relative;
  border-radius: var(--fluent-border-radius-medium);
  overflow: hidden;
  background-color: var(--fluent-neutral-background-2);
  border: 1px solid var(--fluent-neutral-stroke-1);
  transition: all var(--fluent-duration-fast) var(--fluent-curve-easy-ease);
}

.preview-item:hover {
  box-shadow: var(--fluent-shadow-4);
  transform: translateY(-2px);
}

.preview-item img {
  width: 100%;
  height: 120px;
  object-fit: contain;
  display: block;
}

.remove-btn {
  position: absolute;
  top: var(--fluent-space-vertical-xs);
  right: var(--fluent-space-horizontal-xs);
  background-color: var(--fluent-neutral-background-1);
  border: 1px solid var(--fluent-neutral-stroke-1);
  border-radius: var(--fluent-border-radius-circular);
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  font-size: var(--fluent-font-size-200);
  color: var(--fluent-neutral-foreground-rest);
  transition: all var(--fluent-duration-fast) var(--fluent-curve-easy-ease);
}

.remove-btn:hover {
  background-color: #dc3545;
  border-color: #dc3545;
  color: #ffffff;
}

/* Step 4 이미지 분석 결과 스타일 */
.analyzed-image {
  position: relative;
  border-radius: var(--fluent-border-radius-medium);
  overflow: hidden;
  background-color: var(--fluent-neutral-background-2);
  border: 1px solid var(--fluent-neutral-stroke-1);
  transition: all var(--fluent-duration-fast) var(--fluent-curve-easy-ease);
  margin-bottom: var(--fluent-space-vertical-m);
}

.analyzed-image:hover {
  box-shadow: var(--fluent-shadow-4);
  transform: translateY(-2px);
}

.analyzed-image img {
  width: 100%;
  height: 120px;
  object-fit: contain;
  display: block;
}

.image-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--fluent-space-vertical-s) var(--fluent-space-horizontal-m);
  background-color: var(--fluent-neutral-background-1);
  border-bottom: 1px solid var(--fluent-neutral-stroke-1);
}

.image-order {
  font-family: var(--fluent-font-family-base);
  font-size: var(--fluent-font-size-300);
  font-weight: var(--fluent-font-weight-medium);
  color: var(--fluent-neutral-foreground-rest);
}

.image-controls {
  display: flex;
  gap: var(--fluent-space-horizontal-xs);
}

.move-btn {
  background-color: var(--fluent-neutral-background-1);
  border: 1px solid var(--fluent-neutral-stroke-1);
  border-radius: var(--fluent-border-radius-small);
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  font-size: var(--fluent-font-size-200);
  transition: all var(--fluent-duration-fast) var(--fluent-curve-easy-ease);
}

.move-btn:hover:not(:disabled) {
  background-color: var(--fluent-brand-background-1);
  border-color: var(--fluent-brand-background-1);
  color: white;
}

.move-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.image-name {
  font-family: var(--fluent-font-family-base);
  font-size: var(--fluent-font-size-200);
  font-weight: var(--fluent-font-weight-medium);
  color: var(--fluent-neutral-foreground-rest);
  padding: var(--fluent-space-vertical-xs) var(--fluent-space-horizontal-m);
  background-color: var(--fluent-neutral-background-1);
  border-bottom: 1px solid var(--fluent-neutral-stroke-1);
}

.caption-input {
  width: 100%;
  min-height: 60px;
  padding: var(--fluent-space-vertical-s) var(--fluent-space-horizontal-m);
  border: none;
  background-color: var(--fluent-neutral-background-1);
  font-family: var(--fluent-font-family-base);
  font-size: var(--fluent-font-size-300);
  line-height: var(--fluent-line-height-300);
  color: var(--fluent-neutral-foreground-rest);
  resize: vertical;
  outline: none;
}

.caption-input:focus {
  background-color: var(--fluent-neutral-background-2);
  box-shadow: inset 0 0 0 2px var(--fluent-brand-background-1);
}

.ready-btn {
  font-family: var(--fluent-font-family-base);
  font-size: var(--fluent-font-size-300);
  font-weight: var(--fluent-font-weight-medium);
  line-height: var(--fluent-line-height-300);
  color: #ffffff;
  background-color: var(--fluent-brand-background-1);
  border: 1px solid var(--fluent-brand-background-1);
  border-radius: var(--fluent-border-radius-medium);
  padding: var(--fluent-space-vertical-m) var(--fluent-space-horizontal-xl);
  cursor: pointer;
  transition: all var(--fluent-duration-normal) var(--fluent-curve-easy-ease);
  display: block;
  margin: var(--fluent-space-vertical-l) auto 0;
  min-width: 200px;
  text-align: center;
}

.ready-btn:hover:not(:disabled) {
  background-color: var(--fluent-brand-background-2);
  border-color: var(--fluent-brand-background-2);
  transform: translateY(-2px);
  box-shadow: var(--fluent-shadow-8);
}

/* 분석 버튼 */
.analyze-btn {
  font-family: var(--fluent-font-family-base);
  font-size: var(--fluent-font-size-300);
  font-weight: var(--fluent-font-weight-medium);
  line-height: var(--fluent-line-height-300);
  color: #ffffff;
  background-color: var(--fluent-brand-background-1);
  border: 1px solid var(--fluent-brand-background-1);
  border-radius: var(--fluent-border-radius-medium);
  padding: var(--fluent-space-vertical-m) var(--fluent-space-horizontal-xl);
  cursor: pointer;
  transition: all var(--fluent-duration-normal) var(--fluent-curve-easy-ease);
  display: block;
  margin: var(--fluent-space-vertical-xxxl) auto 0;
  min-width: 200px;
  text-align: center;
  position: relative;
  overflow: hidden;
}

.analyze-btn:hover:not(:disabled) {
  background-color: var(--fluent-brand-background-2);
  border-color: var(--fluent-brand-background-2);
  transform: translateY(-2px);
  box-shadow: var(--fluent-shadow-8);
}

.analyze-btn:active:not(:disabled) {
  background-color: var(--fluent-brand-background-3);
  border-color: var(--fluent-brand-background-3);
  transform: translateY(0);
}

.analyze-btn:disabled {
  background-color: var(--fluent-neutral-background-3);
  border-color: var(--fluent-neutral-stroke-1);
  color: var(--fluent-neutral-foreground-disabled);
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.analyze-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left var(--fluent-duration-slow) var(--fluent-curve-easy-ease);
}

.analyze-btn:hover:not(:disabled)::before {
  left: 100%;
}

/* 브레드크럼 네비게이션 */
.breadcrumb-nav {
  background-color: var(--fluent-subtle-background);
  border: 1px solid var(--fluent-neutral-stroke-1);
  border-radius: var(--fluent-border-radius-medium);
  padding: var(--fluent-space-vertical-s) var(--fluent-space-horizontal-l);
  margin-bottom: var(--fluent-space-vertical-l);
  backdrop-filter: blur(10px);
}

.breadcrumb-nav ol {
  list-style: none;
  display: flex;
  align-items: center;
  gap: var(--fluent-space-horizontal-s);
  margin: 0;
  padding: 0;
}

.breadcrumb-item {
  font-family: var(--fluent-font-family-base);
  font-size: var(--fluent-font-size-200);
  font-weight: var(--fluent-font-weight-regular);
  line-height: var(--fluent-line-height-200);
  color: var(--fluent-neutral-foreground-rest);
  transition: all var(--fluent-duration-fast) var(--fluent-curve-easy-ease);
}

.breadcrumb-item:not(:last-child)::after {
  content: '/';
  margin-left: var(--fluent-space-horizontal-s);
  color: var(--fluent-neutral-stroke-accessible);
}

.breadcrumb-item:hover {
  color: var(--fluent-brand-foreground-1);
}

.breadcrumb-item.active {
  color: var(--fluent-brand-foreground-1);
  font-weight: var(--fluent-font-weight-medium);
}

/* 랜딩 페이지 스타일 */
.landing-container {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: var(--fluent-space-vertical-xxxl);
  text-align: center;
}

.welcome-section {
  background-color: var(--fluent-neutral-background-2);
  border: 1px solid var(--fluent-neutral-stroke-1);
  border-radius: var(--fluent-border-radius-x-large);
  padding: var(--fluent-space-vertical-xxxl);
  margin-bottom: var(--fluent-space-vertical-xxxl);
  box-shadow: var(--fluent-shadow-4);
  backdrop-filter: blur(20px);
  max-width: 800px;
  width: 100%;
}

.welcome-title {
  font-family: var(--fluent-font-family-base);
  font-size: var(--fluent-font-size-900);
  font-weight: var(--fluent-font-weight-bold);
  line-height: var(--fluent-line-height-900);
  color: var(--fluent-neutral-foreground-rest);
  margin-bottom: var(--fluent-space-vertical-l);
  background: linear-gradient(135deg, var(--fluent-brand-foreground-1), var(--fluent-brand-foreground-2));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.welcome-subtitle {
  font-family: var(--fluent-font-family-base);
  font-size: var(--fluent-font-size-500);
  font-weight: var(--fluent-font-weight-regular);
  line-height: var(--fluent-line-height-500);
  color: var(--fluent-neutral-foreground-rest);
  margin-bottom: var(--fluent-space-vertical-xxxl);
  opacity: 0.8;
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: var(--fluent-space-horizontal-xl);
  margin: var(--fluent-space-vertical-xxxl) 0;
  padding: 0 var(--fluent-space-horizontal-xl);
  max-width: 1200px;
  width: 100%;
  margin-left: auto;
  margin-right: auto;
}

.feature-card {
  background-color: var(--fluent-neutral-background-2);
  border: 1px solid var(--fluent-neutral-stroke-1);
  border-radius: var(--fluent-border-radius-large);
  padding: var(--fluent-space-vertical-xl);
  text-align: center;
  transition: all var(--fluent-duration-normal) var(--fluent-curve-easy-ease);
  backdrop-filter: blur(10px);
  box-shadow: var(--fluent-shadow-2);
  position: relative;
  overflow: hidden;
}

.feature-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, var(--fluent-brand-background-1), var(--fluent-brand-background-2));
  opacity: 0;
  transition: opacity var(--fluent-duration-normal) var(--fluent-curve-easy-ease);
}

.feature-card:hover {
  transform: translateY(-4px);
  box-shadow: var(--fluent-shadow-8);
  border-color: var(--fluent-brand-stroke-1);
}

.feature-card:hover::before {
  opacity: 1;
}

.feature-icon {
  font-size: var(--fluent-font-size-800);
  margin-bottom: var(--fluent-space-vertical-l);
  display: block;
  background: linear-gradient(135deg, var(--fluent-brand-foreground-1), var(--fluent-brand-foreground-2));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  transition: transform var(--fluent-duration-normal) var(--fluent-curve-easy-ease);
}

.feature-card:hover .feature-icon {
  transform: scale(1.1);
}

.feature-title {
  font-family: var(--fluent-font-family-base);
  font-size: var(--fluent-font-size-500);
  font-weight: var(--fluent-font-weight-semibold);
  line-height: var(--fluent-line-height-500);
  color: var(--fluent-neutral-foreground-rest);
  margin-bottom: var(--fluent-space-vertical-s);
}

.feature-description {
  font-family: var(--fluent-font-family-base);
  font-size: var(--fluent-font-size-300);
  font-weight: var(--fluent-font-weight-regular);
  line-height: var(--fluent-line-height-300);
  color: var(--fluent-neutral-foreground-rest);
  opacity: 0.8;
}

.start-button {
  font-family: var(--fluent-font-family-base);
  font-size: var(--fluent-font-size-400);
  font-weight: var(--fluent-font-weight-medium);
  line-height: var(--fluent-line-height-400);
  color: #ffffff;
  background-color: var(--fluent-brand-background-1);
  border: 1px solid var(--fluent-brand-background-1);
  border-radius: var(--fluent-border-radius-medium);
  padding: var(--fluent-space-vertical-l) var(--fluent-space-horizontal-xxxl);
  cursor: pointer;
  transition: all var(--fluent-duration-normal) var(--fluent-curve-easy-ease);
  text-decoration: none;
  display: inline-block;
  position: relative;
  overflow: hidden;
  min-width: 200px;
  text-align: center;
}

.start-button:hover {
  background-color: var(--fluent-brand-background-2);
  border-color: var(--fluent-brand-background-2);
  transform: translateY(-3px);
  box-shadow: var(--fluent-shadow-16);
}

.start-button:active {
  background-color: var(--fluent-brand-background-3);
  border-color: var(--fluent-brand-background-3);
  transform: translateY(-1px);
}

.start-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left var(--fluent-duration-slow) var(--fluent-curve-easy-ease);
}

.start-button:hover::before {
  left: 100%;
}

/* 로딩 스피너 */
.loading-spinner {
  display: inline-block;
  width: 20px;
  height: 20px;
  border: 2px solid var(--fluent-neutral-stroke-1);
  border-radius: var(--fluent-border-radius-circular);
  border-top-color: var(--fluent-brand-background-1);
  animation: spin var(--fluent-duration-ultra-slow) linear infinite;
  margin-right: var(--fluent-space-horizontal-s);
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* 알림 메시지 */
.alert {
  border-radius: var(--fluent-border-radius-medium);
  padding: var(--fluent-space-vertical-m) var(--fluent-space-horizontal-l);
  margin-bottom: var(--fluent-space-vertical-l);
  border: 1px solid;
  font-family: var(--fluent-font-family-base);
  font-size: var(--fluent-font-size-300);
  font-weight: var(--fluent-font-weight-regular);
  line-height: var(--fluent-line-height-300);
}

.alert.success {
  background-color: #d4edda;
  border-color: #c3e6cb;
  color: #155724;
}

.alert.error {
  background-color: #f8d7da;
  border-color: #f5c6cb;
  color: #721c24;
}

.alert.warning {
  background-color: #fff3cd;
  border-color: #ffeaa7;
  color: #856404;
}

.alert.info {
  background-color: #cce7ff;
  border-color: #b3d9ff;
  color: #004085;
}

/* 반응형 디자인 */
@media (max-width: 768px) {
  .landing-container {
    padding: var(--fluent-space-vertical-l);
  }
  
  .welcome-section {
    padding: var(--fluent-space-vertical-l);
  }
  
  .welcome-title {
    font-size: var(--fluent-font-size-700);
    line-height: var(--fluent-line-height-700);
  }
  
  .welcome-subtitle {
    font-size: var(--fluent-font-size-400);
    line-height: var(--fluent-line-height-400);
  }
  
  .features-grid {
    grid-template-columns: 1fr;
    gap: var(--fluent-space-vertical-l);
  }
  
  .feature-card {
    padding: var(--fluent-space-vertical-l);
  }
  
  .image-preview {
    grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
    gap: var(--fluent-space-horizontal-s);
  }
  
  .preview-item img {
    height: 100px;
  }
  
  .upload-area {
    padding: var(--fluent-space-vertical-l);
  }
  
  .analyze-btn,
  .start-button {
    width: 100%;
    min-width: auto;
  }
  
  /* 키워드 입력 및 이미지 업로드 섹션 반응형 */
  .keyword-input,
  .image-upload,
  .date-selector {
    padding: var(--fluent-space-vertical-l);
  }
  
  .keyword-input h2,
  .image-upload h2,
  .date-selector h2 {
    font-size: var(--fluent-font-size-600);
  }
  
  .date-input {
    max-width: 100%;
    font-size: var(--fluent-font-size-300);
  }
  
  .date-input-group {
    gap: var(--fluent-space-vertical-s);
  }
  
  .unknown-date-checkbox {
    padding: var(--fluent-space-vertical-xs) var(--fluent-space-horizontal-s);
  }
  
  .unknown-date-checkbox label {
    font-size: var(--fluent-font-size-200);
  }
  
  .breadcrumb-nav {
    padding: var(--fluent-space-vertical-s) var(--fluent-space-horizontal-l);
  }
  
  .breadcrumb-item {
    font-size: var(--fluent-font-size-200);
  }
}

@media (max-width: 480px) {
  .welcome-title {
    font-size: var(--fluent-font-size-600);
    line-height: var(--fluent-line-height-600);
  }
  
  .welcome-subtitle {
    font-size: var(--fluent-font-size-300);
    line-height: var(--fluent-line-height-300);
  }
  
  .feature-title {
    font-size: var(--fluent-font-size-400);
    line-height: var(--fluent-line-height-400);
  }
  
  .feature-description {
    font-size: var(--fluent-font-size-200);
    line-height: var(--fluent-line-height-200);
  }
  
  .image-preview {
    grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
  }
  
  .preview-item img {
    height: 80px;
  }
  
  .upload-area {
    padding: var(--fluent-space-vertical-m);
  }
  
  .upload-icon {
    font-size: var(--fluent-font-size-600);
  }
  
  .date-selector h2 {
    font-size: var(--fluent-font-size-500);
    flex-direction: column;
    text-align: center;
    gap: var(--fluent-space-vertical-xs);
  }
  
  .date-input {
    font-size: var(--fluent-font-size-200);
    padding: var(--fluent-space-vertical-s) var(--fluent-space-horizontal-m);
  }
  
  .date-preview {
    padding: var(--fluent-space-vertical-s) var(--fluent-space-horizontal-m);
  }
  
  .date-preview p {
    font-size: var(--fluent-font-size-200);
    text-align: center;
  }
  
  .breadcrumb-nav {
    padding: var(--fluent-space-vertical-xs) var(--fluent-space-horizontal-s);
  }
}