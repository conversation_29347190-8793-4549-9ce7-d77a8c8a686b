# Text Generation Feature Plan

## 목표
`step4.html` 페이지에서 `text-generation-btn` 버튼 클릭 시 Session Storage 에 저장된 데이터를 이용해 OpenAI LLM 으로 블로그용 텍스트(제목, Hero 헤드라인, 위험 경고, 설명 섹션들, CTA)를 생성한다.

## 데이터 소스 (Session Storage)
- key: `step3_complete_data`
  - `dateInfo`: `{ selectedDate, isUnspecified }`
  - `keywordInfo`: `{ modelName, location, additionalPoints }`
  - `images`: 업로드/분석된 이미지 배열 (필요 시 참고)

## 프롬프트 구성

LLM 에 전달할 최종 사용자 프롬프트는 아래 템플릿을 기반으로 Session Storage 값을 치환하여 작성한다.

```text
제목::
{{title}}

블로그 글::
Hero 헤드라인::
{{hero}}

넛지 위험 경고(공포심 자극) 섹션::
{{risk_warning}}

설명 섹션들::
{{explanations}}

넛지 CTA 섹션::
{{cta}}
```

### 동적 변수
| 변수 | 데이터 소스 | 설명 |
|------|-------------|------|
| `{{title}}` | `keywordInfo.modelName`, `keywordInfo.location` | 모델명·지역을 활용해 후킹 제목 작성 |
| `{{hero}}` | `keywordInfo.modelName`, `dateInfo.selectedDate` | 설치 날짜·모델명을 활용한 헤드라인 |
| `{{risk_warning}}` | 자유 작성 | 폭염, 전기료 상승 등 위험 요소 강조 |
| `{{explanations}}` | `images` 분석 결과, `keywordInfo.additionalPoints` | 제품 특징·설치 환경 등을 다단락 설명 |
| `{{cta}}` | 자유 작성 | 상담·문의·구매 유도 문구 |

### 작성 규칙
1. 각 섹션은 블로그 문체로 1~3문장 내외로 작성한다.
2. 섹션 간 자연스러운 연결어를 사용해 흐름을 유지한다.
3. 한국어 사용, 과도한 이모지·외래어는 지양한다.
4. 최종 응답은 `generate_blog_post_sections` 함수 호출 스펙(Strict JSON)에 맞추어 반환한다.
- 날짜·모델명·지역·추가포인트 등을 자연스럽게 반영해 사용자 프롬프트 부분 작성
- 함수 호출 `generate_blog_post_sections` 방식 사용 (strict JSON)

## API 요청 구조
- 클라이언트: `fetch('/openai_proxy.php', {...})` 형태로 프록시 호출 (API Key 노출 방지)
- 요청 Body 예시 (요약):
```json
{
  "model": "o3-2025-04-16",
  "input": [
    { "role": "developer", "content": [{"type":"input_text","text":"Developer Message"}] },
    { "role": "user", "content": [{"type":"input_text","text":"<동적 사용자 프롬프트>"}] }
  ],
  "text": { "format": { "type": "text" } },
  "reasoning": { "effort": "medium", "summary": "auto" },
  "tools": [ { "type":"function", "name":"generate_blog_post_sections", ... } ],
  "store": true
}
```

## 구현 단계
1. **UI 준비**
   - `step4.html`에 `id="text-generation-btn"` 버튼 존재 확인
2. **이벤트 리스너 추가**
   - `js/components/step4DataDisplay.js` 또는 신규 `js/components/textGenerator.js` 파일에서 클릭 이벤트 처리
3. **데이터 수집**
   - `sessionStorage.getItem('step3_complete_data')` 파싱해 필요한 값 추출
4. **프롬프트 빌드**
   - 템플릿 문자열에 동적 데이터 삽입
5. **API 호출**
   - 프록시 PHP/Node (`openai_proxy.php` 등)를 통해 OpenAI API POST
6. **응답 처리**
   - JSON 결과에서 블로그 섹션 추출, 이후 화면 표시 또는 Step5 로 저장
7. **에러 처리/로딩 UI**
8. **보안**
   - 클라이언트 코드에 API Key 절대 포함 X (현 `openai_test.py` 키 제거 필요)

## 다음 작업
- `openai_proxy.php` 인증 헤더 적용 점검
- 결과 렌더링 컴포넌트 작성 (`step5.html` 이동 시 사용 가능)
- 기존 `textGenerationAPI.js` 와 통합 여부 검토