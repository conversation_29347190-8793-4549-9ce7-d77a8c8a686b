<?php
// CORS 헤더 설정
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// OPTIONS 요청 처리 (preflight)
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// URL 파라미터 확인
if (!isset($_GET['url'])) {
    http_response_code(400);
    echo json_encode(['error' => 'URL parameter is required']);
    exit();
}

$imageUrl = $_GET['url'];

// URL 유효성 검사
if (!filter_var($imageUrl, FILTER_VALIDATE_URL)) {
    http_response_code(400);
    echo json_encode(['error' => 'Invalid URL']);
    exit();
}

// OpenAI URL인지 확인 (보안을 위해)
if (strpos($imageUrl, 'oaidalleapiprodscus.blob.core.windows.net') === false) {
    http_response_code(403);
    echo json_encode(['error' => 'Only OpenAI image URLs are allowed']);
    exit();
}

// 이미지 가져오기
$context = stream_context_create([
    'http' => [
        'method' => 'GET',
        'timeout' => 30,
        'user_agent' => 'Mozilla/5.0 (compatible; Image Proxy)'
    ]
]);

$imageData = file_get_contents($imageUrl, false, $context);

if ($imageData === false) {
    http_response_code(500);
    echo json_encode(['error' => 'Failed to fetch image']);
    exit();
}

// 이미지 타입 확인
$finfo = new finfo(FILEINFO_MIME_TYPE);
$mimeType = $finfo->buffer($imageData);

// 이미지 타입이 아닌 경우 차단
if (strpos($mimeType, 'image/') !== 0) {
    http_response_code(400);
    echo json_encode(['error' => 'Not a valid image']);
    exit();
}

// 적절한 헤더 설정
header('Content-Type: ' . $mimeType);
header('Content-Length: ' . strlen($imageData));
header('Cache-Control: public, max-age=3600'); // 1시간 캐시

// 이미지 데이터 출력
echo $imageData;
?>