/**
 * OpenAI Images Edit API를 호출하는 vanilla JavaScript 함수
 */

class OpenAIImageEditor {
    constructor(apiKey) {
        this.apiKey = apiKey;
        this.baseUrl = 'https://api.openai.com/v1/images/edits';
    }

    /**
     * 이미지 편집 API 호출
     * @param {File|string} imageInput - 편집할 이미지 파일 또는 base64 문자열
     * @param {string} prompt - 편집 프롬프트
     * @param {Object} options - 추가 옵션들
     * @returns {Promise<string>} - base64 인코딩된 이미지 데이터
     */
    async editImage(imageInput, prompt, options = {}) {
        try {
            console.log('🎨 OpenAI 이미지 편집 API 호출 시작');
            console.log('프롬프트:', prompt);
            
            // 입력이 base64 문자열인지 File 객체인지 확인
            let imageBlob;
            if (typeof imageInput === 'string') {
                // base64 문자열을 RGBA Blob으로 변환
                console.log('base64 이미지를 RGBA 형식으로 변환 중...');
                imageBlob = await this.convertBase64ToRGBABlob(imageInput);
            } else {
                // File 객체를 그대로 사용
                imageBlob = imageInput;
            }
            
            // FormData 생성
            const formData = new FormData();
            formData.append('image', imageBlob, 'image.png');
            formData.append('prompt', prompt);
            
            // 기본 옵션 설정
            const defaultOptions = {
                model: 'gpt-image-1', // 모델 업데이트
                n: 1,
                size: '1024x1024'

            };
            
            // 옵션 병합 및 FormData에 추가
            const finalOptions = { ...defaultOptions, ...options };
            Object.keys(finalOptions).forEach(key => {
                formData.append(key, finalOptions[key]);
            });

            // API 호출
            const response = await fetch(this.baseUrl, {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${this.apiKey}`
                },
                body: formData
            });

            if (!response.ok) {
                const errorData = await response.json();
                throw new Error(`API Error: ${response.status} - ${errorData.error?.message || 'Unknown error'}`);
            }

            const data = await response.json();
            console.log('✅ 이미지 편집 API 호출 완료');
            return data.data[0].b64_json;
        } catch (error) {
            console.error('❌ 이미지 편집 중 오류 발생:', error);
            throw error;
        }
    }

    /**
     * base64 이미지를 RGBA 형식의 Blob으로 변환
     * @param {string} base64Image - base64 인코딩된 이미지
     * @returns {Promise<Blob>} RGBA 형식의 이미지 Blob
     */
    async convertBase64ToRGBABlob(base64Image) {
        console.log('=== base64 → RGBA 변환 시작 ===');
        console.log('원본 이미지 길이:', base64Image.length);

        // data URL이 누락된 경우 자동으로 추가
        if (!base64Image.startsWith('data:')) {
            base64Image = `data:image/png;base64,${base64Image}`;
        }
 
        return new Promise((resolve, reject) => {
            const img = new Image();
            
            img.onload = () => {
                console.log('이미지 로드 성공:', img.width, 'x', img.height);
                
                const canvas = document.createElement('canvas');
                const ctx = canvas.getContext('2d');
                
                canvas.width = img.width;
                canvas.height = img.height;
                
                // 투명한 배경으로 초기화 (RGBA 보장)
                ctx.fillStyle = 'rgba(255, 255, 255, 0)';
                ctx.fillRect(0, 0, canvas.width, canvas.height);
                
                // 이미지 그리기
                ctx.drawImage(img, 0, 0);
                
                // RGBA 형식의 PNG Blob으로 변환
                canvas.toBlob((blob) => {
                    if (blob) {
                        console.log('✅ RGBA 변환 완료, Blob 크기:', blob.size, 'bytes');
                        resolve(blob);
                    } else {
                        console.error('❌ Blob 생성 실패');
                        reject(new Error('이미지 변환 실패'));
                    }
                }, 'image/png');
            };
            
            img.onerror = (error) => {
                console.error('❌ 이미지 로드 실패:', error);
                reject(new Error('이미지 로드 실패'));
            };
            
            img.src = base64Image;
        });
    }

    /**
     * base64 데이터를 이미지 파일로 다운로드
     * @param {string} base64Data - base64 인코딩된 이미지 데이터
     * @param {string} filename - 저장할 파일명
     */
    downloadImage(base64Data, filename = 'edited-image.png') {
        try {
            // base64를 blob으로 변환
            const byteCharacters = atob(base64Data);
            const byteNumbers = new Array(byteCharacters.length);
            for (let i = 0; i < byteCharacters.length; i++) {
                byteNumbers[i] = byteCharacters.charCodeAt(i);
            }
            const byteArray = new Uint8Array(byteNumbers);
            const blob = new Blob([byteArray], { type: 'image/png' });

            // 다운로드 링크 생성 및 클릭
            const url = URL.createObjectURL(blob);
            const link = document.createElement('a');
            link.href = url;
            link.download = filename;
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
            URL.revokeObjectURL(url);
        } catch (error) {
            console.error('이미지 다운로드 중 오류 발생:', error);
            throw error;
        }
    }

    /**
     * base64 데이터를 이미지 요소로 표시
     * @param {string} base64Data - base64 인코딩된 이미지 데이터
     * @param {HTMLImageElement} imgElement - 표시할 이미지 요소
     */
    displayImage(base64Data, imgElement) {
        imgElement.src = `data:image/png;base64,${base64Data}`;
    }
}

// 사용 예제
async function example() {
    // API 키 설정 (실제 사용시에는 환경변수나 안전한 방법으로 관리)
    const apiKey = 'your-openai-api-key-here';
    const editor = new OpenAIImageEditor(apiKey);

    // 파일 입력에서 이미지 가져오기
    const fileInput = document.getElementById('imageInput');
    const imageFile = fileInput.files[0];

    if (!imageFile) {
        alert('이미지 파일을 선택해주세요.');
        return;
    }

    try {
        // 이미지 편집 실행
        const editedImageBase64 = await editor.editImage(
            imageFile,
            '기존 텍스트와 비율은 유지하면서, 외곽과 배경을 모던하고 명시성이 높게 꾸며야 한다. Edit.',
            {
                size: '1024x1024',
                n: 1
            }
        );

        // 결과 이미지 표시
        const resultImg = document.getElementById('resultImage');
        editor.displayImage(editedImageBase64, resultImg);

        // 다운로드 버튼 활성화
        const downloadBtn = document.getElementById('downloadBtn');
        downloadBtn.onclick = () => {
            editor.downloadImage(editedImageBase64, 'shilla-aircon-edited.png');
        };
        downloadBtn.disabled = false;

    } catch (error) {
        alert(`오류가 발생했습니다: ${error.message}`);
    }
}

// HTML에서 사용할 수 있도록 전역으로 노출
window.OpenAIImageEditor = OpenAIImageEditor;
window.editImageExample = example;