const OpenAIAPI = {
    // API 키 (실제 환경에서는 환경변수나 설정 파일에서 가져와야 함)
    apiKey: "********************************************************************************************************************************************************************",

    async editImage(prompt, base64Image) {
        console.log('OpenAI 이미지 편집 API 호출');
        console.log('프롬프트:', prompt);

        try {
            console.log('원본 이미지 형식:', base64Image.substring(0, 50) + '...');

            // base64 이미지를 RGBA 형식으로 변환
            const rgbaImageBlob = await this.convertToRGBA(base64Image);

            console.log('변환된 blob 타입:', rgbaImageBlob.type);
            console.log('변환된 blob 크기:', rgbaImageBlob.size);

            // FormData 생성
            const formData = new FormData();
            formData.append('image', rgbaImageBlob, 'shilla-aircon.png');
            formData.append('prompt', prompt);
            formData.append('model', 'gpt-image-1');
            formData.append('n', '1');
            formData.append('size', '1024x1024');

            console.log('FormData 생성 완료 (파일명: shilla-aircon.png), API 호출 시작...');

            const response = await fetch('/openai_proxy.php', {
                method: 'POST',
                body: formData
            });

            if (!response.ok) {
                const errorData = await response.json();
                throw new Error(`API 오류: ${errorData.error?.message || response.statusText}`);
            }

            return await response.json();
        } catch (error) {
            console.error('OpenAI API 호출 오류:', error);
            throw error;
        }
    },

    async generateText(prompt, systemMessage = null) {
        console.log('OpenAI 텍스트 생성 API 호출');
        console.log('프롬프트:', prompt);

        try {
            const messages = [];
            if (systemMessage) {
                messages.push({ role: 'system', content: systemMessage });
            }
            messages.push({ role: 'user', content: prompt });

            const response = await fetch('https://api.openai.com/v1/chat/completions', {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${this.apiKey}`,
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    model: 'gpt-4o-mini',
                    messages: messages,
                    max_tokens: 2000,
                    temperature: 0.7
                })
            });

            if (!response.ok) {
                const errorData = await response.json();
                throw new Error(`API 오류: ${errorData.error?.message || response.statusText}`);
            }

            const result = await response.json();
            return result.choices[0].message.content;
        } catch (error) {
            console.error('OpenAI 텍스트 생성 API 호출 오류:', error);
            throw error;
        }
    },

    async convertToRGBA(base64Image) {
        console.log('=== 이미지 변환 시작 ===');
        console.log('원본 이미지 길이:', base64Image.length);
        console.log('이미지 헤더:', base64Image.substring(0, 100));

        return new Promise((resolve, reject) => {
            const img = new Image();

            img.onload = () => {
                console.log('이미지 로드 성공');
                console.log('이미지 크기:', img.width, 'x', img.height);
                console.log('이미지 naturalWidth:', img.naturalWidth, 'naturalHeight:', img.naturalHeight);

                const canvas = document.createElement('canvas');
                const ctx = canvas.getContext('2d');

                canvas.width = img.width;
                canvas.height = img.height;

                console.log('캔버스 크기 설정:', canvas.width, 'x', canvas.height);

                // 투명한 배경으로 캔버스 초기화 (RGBA 보장)
                ctx.fillStyle = 'rgba(255, 255, 255, 0)'; // 완전 투명
                ctx.fillRect(0, 0, canvas.width, canvas.height);

                // 이미지 그리기
                ctx.drawImage(img, 0, 0);
                console.log('이미지 캔버스에 그리기 완료');

                // ImageData를 가져와서 RGBA 형식 확인
                const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
                const channelsPerPixel = imageData.data.length / (canvas.width * canvas.height);
                console.log('이미지 데이터 채널 수:', channelsPerPixel);
                console.log('총 픽셀 수:', canvas.width * canvas.height);
                console.log('총 데이터 길이:', imageData.data.length);

                // RGBA 형식의 PNG로 변환
                canvas.toBlob((blob) => {
                    if (blob) {
                        console.log('✅ RGBA 변환 완료');
                        console.log('Blob 타입:', blob.type);
                        console.log('Blob 크기:', blob.size, 'bytes');
                        console.log('=== 이미지 변환 완료 ===');
                        resolve(blob);
                    } else {
                        console.error('❌ Blob 생성 실패');
                        reject(new Error('이미지 변환 실패'));
                    }
                }, 'image/png');
            };

            img.onerror = (error) => {
                console.error('❌ 이미지 로드 실패:', error);
                console.error('이미지 src:', base64Image.substring(0, 100) + '...');
                reject(new Error('이미지 로드 실패'));
            };

            console.log('이미지 로드 시작...');
            img.src = base64Image;
        });
    }
};