// 텍스트 생성 API 모듈
const TextGenerationAPI = {
    /**
     * 텍스트 생성 API 호출
     * @param {string} prompt - 생성 프롬프트
     * @param {string} systemMessage - 시스템 메시지 (선택사항)
     * @param {Object} options - 생성 옵션
     * @param {string} options.model - 사용할 모델
     * @param {number} options.maxTokens - 최대 토큰 수
     * @param {number} options.temperature - 창의성 수준 (0.0-2.0)
     * @returns {Promise<string>} 생성된 텍스트
     */
    async generateText(prompt, systemMessage = null, options = {}) {
        console.log('📝 텍스트 생성 API 호출 시작');
        console.log('프롬프트 길이:', prompt.length);
        console.log('시스템 메시지:', systemMessage ? '있음' : '없음');

        try {
            // 모델 설정 가져오기
            const modelName = options.model || APIConfig.defaults.textModel;
            const modelConfig = APIConfig.getTextModelConfig(modelName);
            
            console.log('사용 모델:', modelConfig.name);

            // 메시지 배열 구성
            const messages = [];
            if (systemMessage) {
                messages.push({ role: 'system', content: systemMessage });
            }
            messages.push({ role: 'user', content: prompt });

            // API 요청 페이로드
            const payload = {
                model: modelConfig.name,
                messages: messages,
                temperature: options.temperature !== undefined ? options.temperature : modelConfig.temperature
            };

            // 사용자가 명시적으로 maxTokens 값을 전달한 경우에만 추가
            if (options.maxTokens !== undefined && options.maxTokens !== null) {
                payload.max_tokens = options.maxTokens;
            }

            console.log('API 요청 페이로드:', {
                model: payload.model,
                messagesCount: payload.messages.length,
                temperature: payload.temperature,
                hasMaxTokens: 'max_tokens' in payload
            });

            const response = await fetch(APIConfig.endpoints.chatCompletion, {
                method: 'POST',
                headers: APIConfig.getJsonHeaders(),
                body: JSON.stringify(payload)
            });

            if (!response.ok) {
                const errorData = await response.json();
                throw new Error(`API 오류: ${errorData.error?.message || response.statusText}`);
            }

            const result = await response.json();
            
            if (!result.choices || result.choices.length === 0) {
                throw new Error('API 응답에 생성된 텍스트가 없습니다.');
            }

            const generatedText = result.choices[0].message.content;
            console.log('✅ 텍스트 생성 완료');
            console.log('생성된 텍스트 길이:', generatedText.length);
            console.log('사용된 토큰:', result.usage);
            
            return generatedText;
            
        } catch (error) {
            console.error('❌ 텍스트 생성 API 호출 오류:', error);
            throw error;
        }
    },

    /**
     * JSON 형식의 텍스트 생성 및 파싱
     * @param {string} prompt - 생성 프롬프트
     * @param {string} systemMessage - 시스템 메시지
     * @param {Object} options - 생성 옵션
     * @returns {Promise<Object>} 파싱된 JSON 객체
     */
    async generateJsonText(prompt, systemMessage = null, options = {}) {
        console.log('📋 JSON 텍스트 생성 시작');
        
        try {
            const generatedText = await this.generateText(prompt, systemMessage, options);
            return this.parseJsonResponse(generatedText);
        } catch (error) {
            console.error('❌ JSON 텍스트 생성 오류:', error);
            throw error;
        }
    },

    /**
     * 생성된 텍스트에서 JSON 추출 및 파싱
     * @param {string} text - 생성된 텍스트
     * @returns {Object} 파싱된 JSON 객체
     */
    parseJsonResponse(text) {
        console.log('🔍 JSON 파싱 시작');
        console.log('원본 텍스트 길이:', text.length);
        
        try {
            let cleanedText = text.trim();
            console.log('원본 텍스트 샘플:', cleanedText.substring(0, 200) + '...');

            // 코드 블록 제거 (```json ... ``` 형태)
            if (cleanedText.includes('```')) {
                console.log('코드 블록 감지, 제거 중...');
                
                // 첫 번째 { 찾기
                const startIndex = cleanedText.indexOf('{');
                // 마지막 } 찾기
                const endIndex = cleanedText.lastIndexOf('}');

                if (startIndex !== -1 && endIndex !== -1 && endIndex > startIndex) {
                    cleanedText = cleanedText.substring(startIndex, endIndex + 1);
                    console.log('코드 블록 제거 완료');
                } else {
                    console.warn('유효한 JSON 구조를 찾을 수 없음');
                }
            }

            cleanedText = cleanedText.trim();
            console.log('정리된 JSON 길이:', cleanedText.length);
            console.log('정리된 JSON 샘플:', cleanedText.substring(0, 200) + '...');

            // JSON 파싱 시도
            const parsedContent = JSON.parse(cleanedText);
            console.log('✅ JSON 파싱 성공');
            
            return parsedContent;
            
        } catch (parseError) {
            console.error('❌ JSON 파싱 실패:', parseError);
            console.error('파싱 실패한 텍스트:', text);
            throw new Error(`JSON 파싱 실패: ${parseError.message}`);
        }
    },

    /**
     * 생성된 섹션 데이터 검증
     * @param {Object} sections - 생성된 섹션 객체
     * @returns {boolean} 검증 결과
     */
    validateSections(sections) {
        console.log('🔍 섹션 데이터 검증 시작');
        
        const requiredSections = ['hero', 'warning', 'sevenStep', 'warranty', 'review', 'cta'];
        const missingSections = [];
        
        for (const section of requiredSections) {
            if (!sections[section]) {
                missingSections.push(section);
            }
        }
        
        if (missingSections.length > 0) {
            console.warn('❌ 누락된 섹션:', missingSections);
            return false;
        }
        
        // 필수 섹션 내용 검증
        if (!sections.hero || sections.hero.length < 10) {
            console.warn('❌ Hero 섹션이 너무 짧습니다');
            return false;
        }
        
        if (!sections.cta || sections.cta.length < 10) {
            console.warn('❌ CTA 섹션이 너무 짧습니다');
            return false;
        }
        
        console.log('✅ 섹션 데이터 검증 통과');
        return true;
    },

    /**
     * 스트리밍 텍스트 생성 (향후 구현용)
     * @param {string} prompt - 생성 프롬프트
     * @param {string} systemMessage - 시스템 메시지
     * @param {Function} onChunk - 청크 수신 콜백
     * @param {Object} options - 생성 옵션
     * @returns {Promise<string>} 완성된 텍스트
     */
    async generateTextStream(prompt, systemMessage = null, onChunk = null, options = {}) {
        // 향후 스트리밍 API 지원 시 구현
        console.log('📡 스트리밍 텍스트 생성 (현재 미지원)');
        return await this.generateText(prompt, systemMessage, options);
    }
};

// 전역 스코프에 추가
window.TextGenerationAPI = TextGenerationAPI;