// 이미지 편집 API 모듈
const ImageEditAPI = {
    /**
     * 이미지 편집 API 호출
     * @param {string} prompt - 편집 프롬프트
     * @param {string} base64Image - base64 인코딩된 이미지
     * @param {Object} options - 편집 옵션
     * @param {string} options.model - 사용할 모델 ('dall-e-2' 또는 'gpt-image-1')
     * @param {string} options.size - 이미지 크기
     * @param {number} options.n - 생성할 이미지 수
     * @returns {Promise<Object>} API 응답 결과
     */
    async editImage(prompt, base64Image, options = {}) {
        console.log('🎨 이미지 편집 API 호출 시작');
        console.log('프롬프트:', prompt);
        console.log('모델:', options.model || APIConfig.defaults.imageModel);

        try {
            // 모델 설정 가져오기
            const modelName = options.model || APIConfig.defaults.imageModel;
            const modelConfig = APIConfig.getImageModelConfig(modelName);
            
            console.log('모델 설정:', modelConfig);
            console.log('원본 이미지 형식:', base64Image.substring(0, 50) + '...');

            // base64 이미지를 RGBA 형식으로 변환
            const rgbaImageBlob = await this.convertToRGBA(base64Image);
            console.log('변환된 blob 타입:', rgbaImageBlob.type);
            console.log('변환된 blob 크기:', rgbaImageBlob.size);

            // FormData 생성
            const formData = new FormData();
            formData.append('image', rgbaImageBlob, 'image.png');
            formData.append('prompt', prompt);
            formData.append('model', modelConfig.name);
            formData.append('n', options.n || APIConfig.defaults.imageCount);
            formData.append('size', options.size || modelConfig.defaultSize);
            
            // 모델이 response_format을 지원하는 경우에만 추가
            if (modelConfig.supportsResponseFormat) {
                formData.append('response_format', 'url');
            }

            console.log('FormData 생성 완료, API 호출 시작...');

            const response = await fetch(APIConfig.endpoints.imageEdit, {
                method: 'POST',
                headers: APIConfig.getAuthHeaders(),
                body: formData
            });

            if (!response.ok) {
                const errorData = await response.json();
                throw new Error(`API 오류: ${errorData.error?.message || response.statusText}`);
            }

            const result = await response.json();
            console.log('✅ 이미지 편집 API 호출 성공');
            return result;
            
        } catch (error) {
            console.error('❌ 이미지 편집 API 호출 오류:', error);
            throw error;
        }
    },

    /**
     * base64 이미지를 RGBA 형식으로 변환
     * @param {string} base64Image - base64 인코딩된 이미지
     * @returns {Promise<Blob>} RGBA 형식의 PNG Blob
     */
    async convertToRGBA(base64Image) {
        console.log('=== 이미지 RGBA 변환 시작 ===');
        console.log('원본 이미지 길이:', base64Image.length);
        console.log('이미지 헤더:', base64Image.substring(0, 100));

        return new Promise((resolve, reject) => {
            const img = new Image();

            img.onload = () => {
                console.log('이미지 로드 성공');
                console.log('이미지 크기:', img.width, 'x', img.height);

                const canvas = document.createElement('canvas');
                const ctx = canvas.getContext('2d');

                canvas.width = img.width;
                canvas.height = img.height;

                console.log('캔버스 크기 설정:', canvas.width, 'x', canvas.height);

                // 투명한 배경으로 캔버스 초기화 (RGBA 보장)
                ctx.fillStyle = 'rgba(255, 255, 255, 0)';
                ctx.fillRect(0, 0, canvas.width, canvas.height);

                // 이미지 그리기
                ctx.drawImage(img, 0, 0);
                console.log('이미지 캔버스에 그리기 완료');

                // ImageData를 가져와서 RGBA 형식 확인
                const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
                const channelsPerPixel = imageData.data.length / (canvas.width * canvas.height);
                console.log('이미지 데이터 채널 수:', channelsPerPixel);

                // RGBA 형식의 PNG로 변환
                canvas.toBlob((blob) => {
                    if (blob) {
                        console.log('✅ RGBA 변환 완료');
                        console.log('Blob 타입:', blob.type);
                        console.log('Blob 크기:', blob.size, 'bytes');
                        console.log('=== 이미지 RGBA 변환 완료 ===');
                        resolve(blob);
                    } else {
                        console.error('❌ Blob 생성 실패');
                        reject(new Error('이미지 변환 실패'));
                    }
                }, 'image/png');
            };

            img.onerror = (error) => {
                console.error('❌ 이미지 로드 실패:', error);
                console.error('이미지 src:', base64Image.substring(0, 100) + '...');
                reject(new Error('이미지 로드 실패'));
            };

            console.log('이미지 로드 시작...');
            img.src = base64Image;
        });
    },

    /**
     * OpenAI URL에서 이미지를 다운로드하여 data URL로 변환
     * @param {string} imageUrl - 이미지 URL
     * @returns {Promise<string|null>} data URL 또는 null
     */
    async convertUrlToDataUrl(imageUrl) {
        try {
            console.log('🔗 이미지 URL 다운로드 시작:', imageUrl);
            const response = await fetch(imageUrl);
            if (!response.ok) {
                throw new Error(`이미지 다운로드 실패: ${response.status}`);
            }

            const blob = await response.blob();
            console.log('다운로드된 이미지 Blob 크기:', blob.size);

            return new Promise((resolve, reject) => {
                const reader = new FileReader();
                reader.onload = () => {
                    console.log('✅ URL to DataURL 변환 완료');
                    resolve(reader.result);
                };
                reader.onerror = reject;
                reader.readAsDataURL(blob);
            });
        } catch (error) {
            console.error('❌ URL to DataURL 변환 오류:', error);
            return null;
        }
    },

    /**
     * OpenAI base64를 Blob으로 변환 후 data URL 생성
     * @param {string} base64String - base64 문자열
     * @returns {Promise<string|null>} data URL 또는 null
     */
    async convertBase64ToDataUrl(base64String) {
        try {
            console.log('🔄 base64 to DataURL 변환 시작');
            console.log('base64 문자열 길이:', base64String.length);

            // base64를 binary로 변환
            const binaryString = atob(base64String);
            const bytes = new Uint8Array(binaryString.length);
            for (let i = 0; i < binaryString.length; i++) {
                bytes[i] = binaryString.charCodeAt(i);
            }

            // Blob 생성
            const blob = new Blob([bytes], { type: 'image/png' });
            console.log('변환된 Blob 크기:', blob.size);

            // Blob을 data URL로 변환
            return new Promise((resolve, reject) => {
                const reader = new FileReader();
                reader.onload = () => {
                    console.log('✅ base64 to DataURL 변환 완료');
                    resolve(reader.result);
                };
                reader.onerror = reject;
                reader.readAsDataURL(blob);
            });
        } catch (error) {
            console.error('❌ Base64 to DataURL 변환 오류:', error);
            return null;
        }
    },

    /**
     * API 응답에서 이미지 데이터 추출
     * @param {Object} apiResponse - OpenAI API 응답
     * @returns {Promise<string|null>} data URL 또는 null
     */
    async extractImageFromResponse(apiResponse) {
        try {
            if (!apiResponse.data || apiResponse.data.length === 0) {
                throw new Error('API 응답에 이미지 데이터가 없습니다.');
            }

            const imageData = apiResponse.data[0];
            
            if (imageData.url) {
                console.log('URL 형태의 이미지 데이터 처리');
                return await this.convertUrlToDataUrl(imageData.url);
            } else if (imageData.b64_json) {
                console.log('base64 형태의 이미지 데이터 처리');
                return await this.convertBase64ToDataUrl(imageData.b64_json);
            } else {
                throw new Error('지원되지 않는 이미지 데이터 형식입니다.');
            }
        } catch (error) {
            console.error('❌ 이미지 데이터 추출 오류:', error);
            return null;
        }
    }
};

// 전역 스코프에 추가
window.ImageEditAPI = ImageEditAPI;