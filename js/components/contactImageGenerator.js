/**
 * ContactImageGenerator <PERSON><PERSON><PERSON>
 * <PERSON>les creating a contact image using OpenAIImageEditor (image-edit-api.js)
 */

(function (global) {
    "use strict";

    const API_KEY = "********************************************************************************************************************************************************************";

    // Ensure OpenAIImageEditor is available
    if (typeof global.OpenAIImageEditor !== "function") {
        console.error("OpenAIImageEditor is not loaded. Please include image-edit-api.js before this script.");
        return;
    }

    const editorInstance = new global.OpenAIImageEditor(API_KEY);

// 삭제: fetchImageAsBase64 함수 전체

    /**
     * Generate a contact image using the first available base64 image from sessionStorage.
     */
    async function generateContactImage() {
        console.log("=== ContactImageGenerator: Generation started ===");

        // Load pre-generated Base64 string from text file
        let targetBase64;
        try {
            const txtResponse = await fetch("shilla-aircon-base64.txt");
            if (!txtResponse.ok) throw new Error("Failed to load base64 file.");
            targetBase64 = (await txtResponse.text()).trim();
        } catch (e) {
            console.error("Failed to load base64 string:", e);
            alert("이미지 데이터를 불러오는 데 실패했습니다.");
            return;
        }

        try {
            // Prompt for editing
            const prompt = "기존 텍스트의 내용과 비율은 그대로 유지하면서, 외곽만 모던하도록 만들어야 한다.";
            const editedBase64 = await editorInstance.editImage(targetBase64, prompt, { size: "1024x1024", n: 1 });

            // 🔍 디버깅: 응답된 base64 데이터 확인
            console.log("Edited image base64 length:", editedBase64.length);
            console.log("Edited image sample (first 100 chars):", editedBase64.slice(0, 100) + "...");

            // Store edited image in Session Storage for later steps
            sessionStorage.setItem("edited_contact_image", editedBase64);
            console.log("✅ Contact image edited and stored in sessionStorage as 'edited_contact_image'.");

            // 📷 프론트엔드 즉시 미리보기 렌더링
            const buttonsContainer = document.querySelector('.action-buttons-container');
            if (buttonsContainer) {
                // 이전 미리보기 삭제
                const oldPreview = buttonsContainer.querySelector('.contact-image-preview');
                if (oldPreview) oldPreview.remove();

                const previewWrapper = document.createElement('div');
                previewWrapper.className = 'contact-image-preview';
                previewWrapper.style.textAlign = 'center';
                previewWrapper.style.marginBottom = '12px';
                previewWrapper.innerHTML = `<img src="data:image/png;base64,${editedBase64}" alt="연락처 이미지 미리보기" style="max-width:100%; height:auto; border:1px solid #ddd; border-radius:8px;" />`;
                buttonsContainer.insertBefore(previewWrapper, buttonsContainer.firstChild);

                // 🔍 디버깅: DOM에 추가된 프리뷰 요소 확인
                console.log("Preview element added:", previewWrapper);
                const previewImg = previewWrapper.querySelector('img');
                if (previewImg) {
                    console.log("Preview img src (truncated):", previewImg.src.slice(0, 120) + "...");
                }
            }

            // Provide quick feedback to user
            alert("✅ 연락처 이미지 생성이 완료되었습니다! 다음 단계로 진행해주세요.");
        } catch (err) {
            console.error("Error while generating contact image:", err);
            alert("❌ 이미지 생성 중 오류가 발생했습니다: " + err.message);
        }
    }

    // Expose module
    global.ContactImageGenerator = { generateContactImage };
})(window);
