// 이미지 분석 컨트롤러 - 리팩토링된 메인 컨트롤러
const ImageAnalysisController = {
    // 상태 관리
    state: {
        isProcessing: false,
        step3Data: null,
        analyzedImages: [],
        editedImageUrl: null,
        generatedSections: null,
        imageMapping: null,
        currentOptions: {
            imageModel: 'dall-e-2',
            textModel: 'gpt-4o-mini',
            promptType: 'default',
            autoMapping: true
        }
    },

    /**
     * 컨트롤러 초기화
     */
    init() {
        console.log('🎯 이미지 분석 컨트롤러 초기화');
        
        try {
            // 세션 데이터 로드
            this.loadSessionData();
            
            // UI 이벤트 바인딩
            this.bindEvents();
            
            // 초기 UI 상태 설정
            this.updateUI();
            
            console.log('✅ 이미지 분석 컨트롤러 초기화 완료');
        } catch (error) {
            console.error('❌ 컨트롤러 초기화 실패:', error);
            this.showError('컨트롤러 초기화에 실패했습니다.');
        }
    },

    /**
     * 세션 데이터 로드
     */
    loadSessionData() {
        try {
            // step3 데이터 로드
            const step3Data = sessionStorage.getItem('step3Data');
            if (step3Data) {
                this.state.step3Data = JSON.parse(step3Data);
                console.log('Step3 데이터 로드됨:', this.state.step3Data);
            }

            // 분석된 이미지 로드
            const analyzedImages = sessionStorage.getItem('analyzedImages');
            if (analyzedImages) {
                this.state.analyzedImages = JSON.parse(analyzedImages);
                console.log('분석된 이미지 로드됨:', this.state.analyzedImages.length, '개');
            }

            // 기존 생성 결과 로드
            const generatedSections = sessionStorage.getItem('generatedSections');
            if (generatedSections) {
                this.state.generatedSections = JSON.parse(generatedSections);
                console.log('기존 생성 결과 로드됨');
            }

            const imageMapping = sessionStorage.getItem('imageMapping');
            if (imageMapping) {
                this.state.imageMapping = JSON.parse(imageMapping);
                console.log('기존 이미지 매핑 로드됨');
            }

            const editedImageUrl = sessionStorage.getItem('editedImageUrl');
            if (editedImageUrl) {
                this.state.editedImageUrl = editedImageUrl;
                console.log('기존 편집 이미지 로드됨');
            }

        } catch (error) {
            console.error('세션 데이터 로드 실패:', error);
        }
    },

    /**
     * UI 이벤트 바인딩
     */
    bindEvents() {
        // 랜딩페이지 섹션 생성 버튼
        const generateBtn = document.getElementById('generateLandingPageSections');
        if (generateBtn) {
            generateBtn.addEventListener('click', () => this.handleGenerateClick());
        }

        // 모델 선택 이벤트
        const imageModelSelect = document.getElementById('imageModelSelect');
        if (imageModelSelect) {
            imageModelSelect.addEventListener('change', (e) => {
                this.state.currentOptions.imageModel = e.target.value;
            });
        }

        const textModelSelect = document.getElementById('textModelSelect');
        if (textModelSelect) {
            textModelSelect.addEventListener('change', (e) => {
                this.state.currentOptions.textModel = e.target.value;
            });
        }

        // 프롬프트 타입 선택
        const promptTypeSelect = document.getElementById('promptTypeSelect');
        if (promptTypeSelect) {
            promptTypeSelect.addEventListener('change', (e) => {
                this.state.currentOptions.promptType = e.target.value;
            });
        }

        // 자동 매핑 옵션
        const autoMappingCheck = document.getElementById('autoMappingCheck');
        if (autoMappingCheck) {
            autoMappingCheck.addEventListener('change', (e) => {
                this.state.currentOptions.autoMapping = e.target.checked;
            });
        }

        // 미리보기 버튼
        const previewBtn = document.getElementById('previewGeneration');
        if (previewBtn) {
            previewBtn.addEventListener('click', () => this.handlePreviewClick());
        }

        // 재생성 버튼
        const regenerateBtn = document.getElementById('regenerateSections');
        if (regenerateBtn) {
            regenerateBtn.addEventListener('click', () => this.handleRegenerateClick());
        }
    },

    /**
     * 생성 버튼 클릭 핸들러
     */
    async handleGenerateClick() {
        if (this.state.isProcessing) {
            console.log('이미 처리 중입니다.');
            return;
        }

        try {
            // 입력 데이터 유효성 검증
            this.validateInputData();

            // 처리 시작
            this.state.isProcessing = true;
            this.updateUI();

            // 진행상황 표시
            this.showProgress('랜딩페이지 섹션 생성을 시작합니다...');

            // 1단계: 이미지 편집
            await this.executeImageEditing();

            // 2단계: 콘텐츠 생성
            await this.executeContentGeneration();

            // 3단계: 이미지 매핑
            await this.executeImageMapping();

            // 4단계: 결과 저장 및 표시
            await this.saveAndDisplayResults();

            // 성공 메시지
            this.showSuccess('랜딩페이지 섹션이 성공적으로 생성되었습니다!');

        } catch (error) {
            console.error('생성 프로세스 실패:', error);
            this.showError(`생성 실패: ${error.message}`);
        } finally {
            this.state.isProcessing = false;
            this.updateUI();
        }
    },

    /**
     * 입력 데이터 유효성 검증
     */
    validateInputData() {
        if (!this.state.step3Data) {
            throw new Error('3단계 데이터가 없습니다. 이전 단계를 완료해주세요.');
        }

        if (!this.state.analyzedImages || this.state.analyzedImages.length === 0) {
            throw new Error('분석된 이미지가 없습니다.');
        }

        if (!this.state.analyzedImages[0] || !this.state.analyzedImages[0].base64) {
            throw new Error('첫 번째 이미지 데이터가 유효하지 않습니다.');
        }

        // Shilla Aircon Base64 데이터 확인
        if (!window.shillaAirconBase64) {
            throw new Error('Shilla 에어컨 이미지 데이터가 없습니다.');
        }

        console.log('✅ 입력 데이터 유효성 검증 통과');
    },

    /**
     * 이미지 편집 실행
     */
    async executeImageEditing() {
        this.showProgress('이미지 편집 중...');
        
        try {
            const firstImage = this.state.analyzedImages[0];
            const baseImage = firstImage.base64 || window.shillaAirconBase64;

            this.state.editedImageUrl = await ImageEditorService.editImage(baseImage, {
                promptType: this.state.currentOptions.promptType,
                model: this.state.currentOptions.imageModel,
                onProgress: (message) => this.showProgress(`이미지 편집: ${message}`)
            });

            console.log('✅ 이미지 편집 완료');
        } catch (error) {
            console.error('이미지 편집 실패:', error);
            throw new Error(`이미지 편집 실패: ${error.message}`);
        }
    },

    /**
     * 콘텐츠 생성 실행
     */
    async executeContentGeneration() {
        this.showProgress('콘텐츠 생성 중...');
        
        try {
            this.state.generatedSections = await ContentGeneratorService.generateSections(
                this.state.step3Data,
                this.state.analyzedImages,
                this.state.editedImageUrl,
                {
                    model: this.state.currentOptions.textModel,
                    onProgress: (message) => this.showProgress(`콘텐츠 생성: ${message}`)
                }
            );

            console.log('✅ 콘텐츠 생성 완료');
        } catch (error) {
            console.error('콘텐츠 생성 실패:', error);
            throw new Error(`콘텐츠 생성 실패: ${error.message}`);
        }
    },

    /**
     * 이미지 매핑 실행
     */
    async executeImageMapping() {
        this.showProgress('이미지 매핑 중...');
        
        try {
            this.state.imageMapping = ImageMapperService.createImageMapping(
                this.state.analyzedImages,
                this.state.generatedSections,
                {
                    autoMapping: this.state.currentOptions.autoMapping
                }
            );

            console.log('✅ 이미지 매핑 완료');
        } catch (error) {
            console.error('이미지 매핑 실패:', error);
            // 매핑 실패는 치명적이지 않으므로 경고만 표시
            console.warn('이미지 매핑을 건너뜁니다.');
            this.state.imageMapping = { mappings: {}, suggestions: {} };
        }
    },

    /**
     * 결과 저장 및 표시
     */
    async saveAndDisplayResults() {
        this.showProgress('결과 저장 및 표시 중...');
        
        try {
            // 세션 스토리지에 저장
            sessionStorage.setItem('generatedSections', JSON.stringify(this.state.generatedSections));
            sessionStorage.setItem('imageMapping', JSON.stringify(this.state.imageMapping));
            sessionStorage.setItem('editedImageUrl', this.state.editedImageUrl);
            sessionStorage.setItem('generationTimestamp', new Date().toISOString());

            // UI에 결과 표시
            this.displayResults();

            console.log('✅ 결과 저장 및 표시 완료');
        } catch (error) {
            console.error('결과 저장 실패:', error);
            throw new Error(`결과 저장 실패: ${error.message}`);
        }
    },

    /**
     * 결과 표시
     */
    displayResults() {
        // 생성된 섹션 표시
        this.displayGeneratedSections();
        
        // 이미지 매핑 표시
        this.displayImageMapping();
        
        // 다음 단계 버튼 활성화
        this.enableNextStepButton();
    },

    /**
     * 생성된 섹션 표시
     */
    displayGeneratedSections() {
        const container = document.getElementById('generatedSectionsContainer');
        if (!container || !this.state.generatedSections) {
            return;
        }

        let html = '<div class="generated-sections">';
        html += '<h3>생성된 랜딩페이지 섹션</h3>';

        // 각 섹션 표시
        Object.entries(this.state.generatedSections).forEach(([sectionName, sectionData]) => {
            html += `
                <div class="section-item" data-section="${sectionName}">
                    <h4>${sectionData.title || sectionName}</h4>
                    <div class="section-content">
                        ${sectionData.content || ''}
                    </div>
                    ${sectionData.image ? `<img src="${sectionData.image}" alt="${sectionName}" class="section-image">` : ''}
                </div>
            `;
        });

        html += '</div>';
        container.innerHTML = html;
    },

    /**
     * 이미지 매핑 표시
     */
    displayImageMapping() {
        const container = document.getElementById('imageMappingContainer');
        if (!container || !this.state.imageMapping) {
            return;
        }

        const displayMapping = ImageMapperService.formatMappingForDisplay(
            this.state.imageMapping,
            this.state.analyzedImages,
            this.state.generatedSections
        );

        let html = '<div class="image-mapping">';
        html += '<h3>이미지 매핑 결과</h3>';
        
        // 매핑 요약
        html += `
            <div class="mapping-summary">
                <p>총 이미지: ${displayMapping.summary.totalImages}개</p>
                <p>매핑된 이미지: ${displayMapping.summary.mappedImages}개</p>
                <p>매핑 품질: ${displayMapping.summary.qualityLevel}</p>
            </div>
        `;

        // 성공적인 매핑
        if (displayMapping.successful.length > 0) {
            html += '<h4>매핑된 이미지</h4>';
            displayMapping.successful.forEach(item => {
                html += `
                    <div class="mapping-item success">
                        <span class="image-name">${item.imageName}</span>
                        <span class="arrow">→</span>
                        <span class="section-name">${item.sectionTitle}</span>
                        <span class="confidence">(신뢰도: ${(item.confidence * 100).toFixed(0)}%)</span>
                    </div>
                `;
            });
        }

        // 제안사항
        if (displayMapping.suggestions.length > 0) {
            html += '<h4>매핑 제안</h4>';
            displayMapping.suggestions.forEach(item => {
                html += `
                    <div class="mapping-item suggestion">
                        <span class="image-name">${item.imageName}</span>
                        <div class="suggestions">
                `;
                item.suggestions.forEach(suggestion => {
                    html += `
                        <div class="suggestion-item">
                            <span class="section-name">${suggestion.sectionTitle}</span>
                            <span class="confidence">(${(suggestion.confidence * 100).toFixed(0)}%)</span>
                        </div>
                    `;
                });
                html += '</div></div>';
            });
        }

        html += '</div>';
        container.innerHTML = html;
    },

    /**
     * 다음 단계 버튼 활성화
     */
    enableNextStepButton() {
        const nextBtn = document.getElementById('goToStep5');
        if (nextBtn) {
            nextBtn.disabled = false;
            nextBtn.style.display = 'block';
        }
    },

    /**
     * 미리보기 클릭 핸들러
     */
    handlePreviewClick() {
        try {
            this.validateInputData();
            
            // 이미지 편집 미리보기
            const imagePreview = ImageEditorService.previewEdit(
                this.state.currentOptions.promptType
            );
            
            // 콘텐츠 생성 미리보기
            const contentPreview = ContentGeneratorService.previewGeneration(
                this.state.step3Data,
                this.state.analyzedImages
            );
            
            // 미리보기 모달 표시
            this.showPreviewModal(imagePreview, contentPreview);
            
        } catch (error) {
            this.showError(`미리보기 실패: ${error.message}`);
        }
    },

    /**
     * 재생성 클릭 핸들러
     */
    async handleRegenerateClick() {
        if (confirm('기존 결과를 삭제하고 다시 생성하시겠습니까?')) {
            // 기존 결과 초기화
            this.state.generatedSections = null;
            this.state.imageMapping = null;
            this.state.editedImageUrl = null;
            
            // 세션 스토리지 정리
            sessionStorage.removeItem('generatedSections');
            sessionStorage.removeItem('imageMapping');
            sessionStorage.removeItem('editedImageUrl');
            
            // UI 업데이트
            this.updateUI();
            
            // 재생성 실행
            await this.handleGenerateClick();
        }
    },

    /**
     * UI 상태 업데이트
     */
    updateUI() {
        // 버튼 상태 업데이트
        const generateBtn = document.getElementById('generateLandingPageSections');
        if (generateBtn) {
            generateBtn.disabled = this.state.isProcessing;
            generateBtn.textContent = this.state.isProcessing ? '생성 중...' : '랜딩페이지 섹션 생성';
        }

        // 모델 선택 UI 업데이트
        this.updateModelSelectors();
        
        // 결과 표시 여부
        if (this.state.generatedSections) {
            this.displayResults();
        }
    },

    /**
     * 모델 선택기 업데이트
     */
    updateModelSelectors() {
        // 이미지 모델 선택기
        const imageModelSelect = document.getElementById('imageModelSelect');
        if (imageModelSelect && imageModelSelect.children.length === 0) {
            const imageModels = ImageEditorService.getSupportedModels();
            imageModels.forEach(model => {
                const option = document.createElement('option');
                option.value = model.name;
                option.textContent = model.displayName;
                imageModelSelect.appendChild(option);
            });
        }

        // 텍스트 모델 선택기
        const textModelSelect = document.getElementById('textModelSelect');
        if (textModelSelect && textModelSelect.children.length === 0) {
            const textModels = ContentGeneratorService.getSupportedModels();
            textModels.forEach(model => {
                const option = document.createElement('option');
                option.value = model.name;
                option.textContent = model.displayName;
                textModelSelect.appendChild(option);
            });
        }

        // 프롬프트 타입 선택기
        const promptTypeSelect = document.getElementById('promptTypeSelect');
        if (promptTypeSelect && promptTypeSelect.children.length === 0) {
            const promptTypes = ImageEditorService.getPromptTypes();
            promptTypes.forEach(type => {
                const option = document.createElement('option');
                option.value = type.type;
                option.textContent = `${type.name} - ${type.description}`;
                promptTypeSelect.appendChild(option);
            });
        }
    },

    /**
     * 진행상황 표시
     */
    showProgress(message) {
        console.log('📋', message);
        
        const progressElement = document.getElementById('progressMessage');
        if (progressElement) {
            progressElement.textContent = message;
            progressElement.style.display = 'block';
        }
    },

    /**
     * 성공 메시지 표시
     */
    showSuccess(message) {
        console.log('✅', message);
        
        const progressElement = document.getElementById('progressMessage');
        if (progressElement) {
            progressElement.textContent = message;
            progressElement.className = 'success';
            
            setTimeout(() => {
                progressElement.style.display = 'none';
                progressElement.className = '';
            }, 3000);
        }
    },

    /**
     * 오류 메시지 표시
     */
    showError(message) {
        console.error('❌', message);
        
        const progressElement = document.getElementById('progressMessage');
        if (progressElement) {
            progressElement.textContent = message;
            progressElement.className = 'error';
        }
        
        alert(message);
    },

    /**
     * 미리보기 모달 표시
     */
    showPreviewModal(imagePreview, contentPreview) {
        const modal = document.createElement('div');
        modal.className = 'preview-modal';
        modal.innerHTML = `
            <div class="modal-content">
                <h3>생성 미리보기</h3>
                <div class="preview-section">
                    <h4>이미지 편집</h4>
                    <p><strong>프롬프트:</strong> ${imagePreview.prompt}</p>
                    <p><strong>모델:</strong> ${imagePreview.model}</p>
                    <p><strong>예상 시간:</strong> ${imagePreview.estimatedTime}</p>
                </div>
                <div class="preview-section">
                    <h4>콘텐츠 생성</h4>
                    <p><strong>모델:</strong> ${contentPreview.model}</p>
                    <p><strong>예상 시간:</strong> ${contentPreview.estimatedTime}</p>
                    <p><strong>예상 비용:</strong> ${contentPreview.estimatedCost}</p>
                </div>
                <div class="modal-buttons">
                    <button onclick="this.parentElement.parentElement.parentElement.remove()">닫기</button>
                </div>
            </div>
        `;
        
        document.body.appendChild(modal);
    },

    /**
     * 레거시 함수들 (기존 코드와의 호환성을 위해 유지)
     */
    
    // 기존 generateLandingPageSections 함수 래퍼
    generateLandingPageSections() {
        return this.handleGenerateClick();
    },

    // 기존 analyzeImageCaptions 함수
    analyzeImageCaptions() {
        return this.state.analyzedImages || [];
    },

    // 기존 createImageMapping 함수
    createImageMapping() {
        if (this.state.imageMapping) {
            return this.state.imageMapping.mappings || {};
        }
        return {};
    }
};

// 전역 스코프에 추가 (기존 코드와의 호환성)
window.ImageAnalysisController = ImageAnalysisController;
window.ImageAnalysis = ImageAnalysisController; // 기존 이름으로도 접근 가능

// DOM 로드 완료 시 자동 초기화
document.addEventListener('DOMContentLoaded', () => {
    ImageAnalysisController.init();
});