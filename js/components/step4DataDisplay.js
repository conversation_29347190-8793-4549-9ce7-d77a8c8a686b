/**
 * Step 4 Data Display Module
 * Session Storage에서 데이터를 가져와서 step4-content 클래스에 표시하는 모듈
 */

const Step4DataDisplay = {
    /**
     * 초기화 함수
     */
    init() {
        console.log('=== Step4DataDisplay 초기화 시작 ===');
        this.loadAndDisplaySessionData();
        this.addActionButtons();
    },

    /**
     * Session Storage에서 모든 데이터를 가져와서 표시
     */
    loadAndDisplaySessionData() {
        const container = document.querySelector('.step4-content');
        if (!container) {
            console.error('step4-content 클래스를 가진 요소를 찾을 수 없습니다.');
            return;
        }

        // Session Storage의 모든 데이터 수집
        const sessionData = this.getAllSessionStorageData();
        
        // 데이터 표시
        this.displaySessionData(container, sessionData);
    },

    /**
     * Session Storage의 모든 데이터를 수집
     */
    getAllSessionStorageData() {
        const data = {};
        
        // 모든 Session Storage 키 순회
        for (let i = 0; i < sessionStorage.length; i++) {
            const key = sessionStorage.key(i);
            try {
                const value = sessionStorage.getItem(key);
                // JSON 파싱 시도
                try {
                    data[key] = JSON.parse(value);
                } catch {
                    // JSON이 아닌 경우 문자열로 저장
                    data[key] = value;
                }
            } catch (error) {
                console.error(`Session Storage 키 '${key}' 읽기 오류:`, error);
            }
        }
        
        console.log('수집된 Session Storage 데이터:', data);
        return data;
    },

    /**
     * Session Storage 데이터를 화면에 표시 (step3_complete_data만)
     */
    displaySessionData(container, sessionData) {
        let html = '<h2>🖼️ 이미지 관련 Session Storage 데이터</h2>';
        
        // step3_complete_data 키만 표시
        const targetKey = 'step3_complete_data';
        const targetData = sessionData[targetKey];
        
        // 데이터가 없는 경우
        if (!targetData) {
            html += '<div class="no-data-message">📭 \'step3_complete_data\' Session Storage 데이터가 없습니다.</div>';
            container.innerHTML = html;
            return;
        }

        html += '<div class="session-data-container">';

        // step3_complete_data만 표시
        html += this.createDataCard(targetKey, targetData, true);

        html += '</div>';

        container.innerHTML = html;
    },

    /**
     * 개별 데이터 카드 생성
     */
    createDataCard(key, value, isPriority = false) {
        const cardClass = isPriority ? 'data-card priority-card' : 'data-card';
        const icon = this.getIconForKey(key);
        const displayName = this.getDisplayNameForKey(key);
        
        let content = '';
        
        if (typeof value === 'object' && value !== null) {
            content = this.formatObjectData(value);
        } else {
            content = `<div class="simple-value">${this.escapeHtml(String(value))}</div>`;
        }

        return `
            <div class="${cardClass}">
                <div class="card-header">
                    <h4>${icon} ${displayName}</h4>
                    <span class="key-name">(${key})</span>
                </div>
                <div class="card-content">
                    ${content}
                </div>
            </div>
        `;
    },

    /**
     * 객체 데이터를 포맷팅
     */
    formatObjectData(obj) {
        if (Array.isArray(obj)) {
            // images 배열인지 확인
            if (obj.length > 0 && obj[0].base64 && obj[0].caption) {
                return this.formatImagesArray(obj);
            }
            return `<div class="array-data">배열 (${obj.length}개 항목)</div>` + 
                   this.formatArrayPreview(obj);
        }
        
        let html = '<div class="object-data">';
        // 제외할 필드들
        const excludeFields = ['dateInfo', 'keywordInfo', 'timestamp', 'totalImages'];
        
        Object.keys(obj).filter(key => !excludeFields.includes(key)).slice(0, 5).forEach(key => {
            const value = obj[key];
            
            if (typeof value === 'object' && value !== null) {
                if (Array.isArray(value)) {
                    // images 배열인지 확인하고 실제 이미지로 표시
                    if (key === 'images' && value.length > 0 && value[0].base64) {
                        html += `<div class="object-item"><strong>${key}:</strong></div>`;
                        html += this.formatImagesArray(value);
                        return; // 다음 키로 넘어감
                    } else {
                        html += `<div class="object-item"><strong>${key}:</strong> 배열 (${value.length}개)</div>`;
                    }
                } else {
                    html += `<div class="object-item"><strong>${key}:</strong> 객체</div>`;
                }
            } else {
                const displayValue = String(value).length > 50 ? 
                    String(value).substring(0, 50) + '...' : 
                    String(value);
                html += `<div class="object-item"><strong>${key}:</strong> ${this.escapeHtml(displayValue)}</div>`;
            }
        });
        
        if (Object.keys(obj).length > 5) {
            html += `<div class="more-items">... 외 ${Object.keys(obj).length - 5}개 항목</div>`;
        }
        
        html += '</div>';
        return html;
    },

    /**
     * 이미지 배열을 깔끔하게 포맷팅
     */
    formatImagesArray(images) {
        let html = `<div class="images-array-container">`;
        html += `<div class="images-header">📷 이미지 ${images.length}개</div>`;
        
        images.forEach((image, index) => {
            html += `<div class="image-item">`;
            
            // 이미지 썸네일 (200px 너비, 비율 유지)
            if (image.base64) {
                html += `<div class="image-thumbnail-200">`;
                html += `<img src="${image.base64}" alt="${image.filename || image.name || `이미지 ${index + 1}`}" style="width: 200px; height: auto;" />`;
                html += `</div>`;
            }
            
            // 이미지 정보
            html += `<div class="image-info">`;
            
            // 파일명
            if (image.filename) {
                html += `<div class="image-filename">📄 <strong>파일명:</strong> ${this.escapeHtml(image.filename)}</div>`;
            }
            
            // 캡션
            if (image.caption) {
                html += `<div class="image-caption">💬 <strong>캡션:</strong> ${this.escapeHtml(image.caption)}</div>`;
            }
            
            // 추가 정보
            const additionalInfo = [];
            if (image.analysis) {
                additionalInfo.push('🔍 분석 완료');
            }
            if (image.edited) {
                additionalInfo.push('✏️ 편집됨');
            }
            
            if (additionalInfo.length > 0) {
                html += `<div class="image-status">${additionalInfo.join(' • ')}</div>`;
            }
            
            html += `</div>`; // image-info 끝
            html += `</div>`; // image-item 끝
        });
        
        html += `</div>`; // images-array-container 끝
        return html;
    },

    /**
     * 배열 미리보기 생성
     */
    formatArrayPreview(arr) {
        if (arr.length === 0) return '<div class="empty-array">빈 배열</div>';
        
        let html = '<div class="array-preview">';
        arr.slice(0, 3).forEach((item, index) => {
            let preview = '';
            if (typeof item === 'object' && item !== null) {
                preview = '객체';
            } else {
                preview = String(item).length > 30 ? 
                    String(item).substring(0, 30) + '...' : 
                    String(item);
            }
            html += `<div class="array-item">[${index}] ${this.escapeHtml(preview)}</div>`;
        });
        
        if (arr.length > 3) {
            html += `<div class="more-items">... 외 ${arr.length - 3}개 항목</div>`;
        }
        
        html += '</div>';
        return html;
    },

    /**
     * 키에 따른 아이콘 반환
     */
    getIconForKey(key) {
        const iconMap = {
            'step3_complete_data': '📋',
            'step3Data': '📄',
            'shilla_date_data': '📅',
            'shilla_keyword_data': '🔧',
            'shilla_image_data': '🖼️',
            'shilla_analysis_data': '🔍',
            'shilla_content_data': '📝',
            'shilla_current_step': '👣',
            'analyzedImages': '🔬',
            'generatedSections': '📑',
            'imageMapping': '🗺️',
            'editedImageUrl': '✏️'
        };
        
        return iconMap[key] || '📦';
    },

    /**
     * 키에 따른 표시명 반환
     */
    getDisplayNameForKey(key) {
        const nameMap = {
            'step3_complete_data': 'Step 3 완료 데이터',
            'step3Data': 'Step 3 데이터',
            'shilla_date_data': '날짜 데이터',
            'shilla_keyword_data': '키워드 데이터',
            'shilla_image_data': '이미지 데이터',
            'shilla_analysis_data': '분석 데이터',
            'shilla_content_data': '콘텐츠 데이터',
            'shilla_current_step': '현재 단계',
            'analyzedImages': '분석된 이미지',
            'generatedSections': '생성된 섹션',
            'imageMapping': '이미지 매핑',
            'editedImageUrl': '편집된 이미지 URL'
        };
        
        return nameMap[key] || key;
    },

    /**
     * HTML 이스케이프
     */
    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    },

    /**
     * 액션 버튼들을 step-container 하단에 추가
     */
    addActionButtons() {
        const stepContainer = document.getElementById('step-container');
        if (!stepContainer) {
            console.error('step-container를 찾을 수 없습니다.');
            return;
        }

        // 버튼 컨테이너 생성
        const buttonContainer = document.createElement('div');
        buttonContainer.className = 'action-buttons-container';
        buttonContainer.innerHTML = `
            <div class="action-buttons">
                <button id="contact-image-btn" class="action-btn primary-btn" style="display: none;">
                    📞 연락처 이미지 생성
                </button>
                <button id="text-generation-btn" class="action-btn secondary-btn">
                    📝 텍스트 생성
                </button>
            </div>
            <!-- 생성된 텍스트가 표시될 영역 -->
            <div id="generated-text-container" class="generated-text-container"></div>
        `;

        // step-container 하단에 추가
        stepContainer.appendChild(buttonContainer);

        // 이벤트 리스너 추가
        this.attachButtonEventListeners();
    },

    /**
     * 버튼 이벤트 리스너 추가
     */
    attachButtonEventListeners() {
        const contactImageBtn = document.getElementById('contact-image-btn');
        const textGenerationBtn = document.getElementById('text-generation-btn');

        if (contactImageBtn) {
            contactImageBtn.addEventListener('click', () => {
                console.log('연락처 이미지 생성 버튼 클릭');
                this.handleContactImageGeneration();
            });
        }

        if (textGenerationBtn) {
            textGenerationBtn.addEventListener('click', () => {
                console.log('텍스트 생성 버튼 클릭');
                this.handleTextGeneration();
            });
        }
    },

    /**
     * 연락처 이미지 생성 처리
     */
    handleContactImageGeneration() {
        if (window.ContactImageGenerator && typeof ContactImageGenerator.generateContactImage === 'function') {
            ContactImageGenerator.generateContactImage();
        } else {
            alert('⚠️ ContactImageGenerator 모듈을 로드하지 못했습니다.');
        }
    },

    /**
     * 텍스트 생성 처리
     */
    handleTextGeneration() {
        console.log('📝 텍스트 생성 로직 시작');

        // 1) 세션 데이터 확보
        let step3Raw = sessionStorage.getItem('step3_complete_data');
        if (!step3Raw) {
            alert('📭 step3_complete_data 가 없습니다. 먼저 3단계를 완료하세요.');
            return;
        }
        let step3;
        try {
            step3 = JSON.parse(step3Raw);
        } catch (e) {
            console.error('JSON 파싱 실패', e);
            alert('step3 데이터 파싱 오류');
            return;
        }

        // 2) 이미지 캡션 목록 수집
        const captions = (step3.images || [])
            .filter(img => img.caption && img.caption.trim())
            .map(img => img.caption.trim());

        // 3) 프롬프트 빌드
        const prompt = this.buildPrompt(step3, captions);
        console.log('생성 프롬프트:', prompt.substring(0, 500) + '...');

        // 4) LLM 호출 (JSON 반환 기대)
        const systemMsg = 'You are an expert Korean blog copywriter who returns ONLY valid JSON.';
        TextGenerationAPI.generateJsonText(prompt, systemMsg, {
            model: 'o3'
        }).then(json => {
            console.log('LLM 결과 JSON:', json);
            // 5) 유효성 검사 및 저장
            sessionStorage.setItem('generatedSections', JSON.stringify(json));
            this.renderGeneratedSections(json);
        }).catch(err => {
            console.error('텍스트 생성 실패', err);
            alert('❌ 텍스트 생성 실패: ' + err.message);
        });
    },

    /**
     * Step3 데이터와 이미지 캡션을 이용해 프롬프트 문자열 생성
     */
    buildPrompt(step3, captions = []) {
        const { dateInfo = {}, keywordInfo = {} } = step3;
        const dateText = dateInfo.isUnspecified ? '날짜 미상' : (dateInfo.selectedDate || '날짜 미상');
        const { modelName = '', location = '', additionalPoints = '' } = keywordInfo;

        // 설명 섹션에 캡션을 삽입하기 위한 지시어 생성
        let captionInstruction = '';
        if (captions.length > 0) {
            captionInstruction = `다음 이미지 캡션을 참고하여 설명 섹션에 자연스럽게 포함하세요: ${captions.join(', ')}.`;
        }

        // PromptConfig의 텍스트 생성 프롬프트 사용해 일관성 유지
        return PromptConfig.getTextGenerationPrompt(step3, (step3.images || []).map(img => ({ caption: img.caption || '' })));

    },

    /**
     * 생성된 섹션 JSON을 화면에 렌더링
     */
    renderGeneratedSections(sections) {
        // 세션스토리지에서 이미지 및 매핑 정보 가져오기
        let analyzedImages = []; // ✅ 초기화
        console.log('[Step4] analyzedImages initialized:', analyzedImages);
        let imageMapping = null;
        try {
            const imgsRaw = sessionStorage.getItem('analyzedImages');
            const mappingRaw = sessionStorage.getItem('imageMapping');
            if (imgsRaw) analyzedImages = JSON.parse(imgsRaw);
            // analyzedImages가 없으면 다양한 fallback 키들에서 데이터 탐색
            if ((!imgsRaw || analyzedImages.length === 0)) {
                console.log('[Step4] analyzedImages가 sessionStorage 에서 발견되지 않음, fallback 키를 확인합니다.');
                const fallbackKeys = ['shilla_image_data', 'shilla_analysis_data', 'step3_complete_data', 'step3Data'];
                let found = false;
                for (const fbKey of fallbackKeys) {
                    const fbRaw = sessionStorage.getItem(fbKey);
                    if (!fbRaw) continue;
                    try {
                        const fbParsed = JSON.parse(fbRaw);
                        const candidate = Array.isArray(fbParsed) ? fbParsed :
                                          (Array.isArray(fbParsed.images) ? fbParsed.images : null);
                        if (candidate && candidate.length > 0) {
                            analyzedImages = candidate;
                            console.log(`[Step4] '${fbKey}' 에서 analyzedImages 확보 (${candidate.length}개)`);
                            found = true;
                            break;
                        }
                    } catch(e){
                        console.warn(`[Step4] '${fbKey}' 파싱 오류`, e);
                    }
                }
                if (!found) {
                    console.warn('[Step4] analyzedImages 를 어떤 키에서도 찾지 못했습니다.');
                }
            }
            if (mappingRaw) {
                const parsed = JSON.parse(mappingRaw);
                // imageMapper 저장 구조가 {mappings:{}, ...} 이거나 바로 매핑일 수 있음
                imageMapping = parsed.mappings ? parsed.mappings : parsed;
            }
        } catch (e) {
            console.warn('이미지 매핑 데이터 파싱 오류', e);
        }

        // 매핑 정보가 없으면 자동 매핑 시도 (ImageMapperService 사용)
        if ((!imageMapping || Object.keys(imageMapping).length === 0) && analyzedImages.length > 0 && window.ImageMapperService) {
            try {
                const autoResult = window.ImageMapperService.createImageMapping(analyzedImages, sections || {}, { autoMapping: true });
                imageMapping = autoResult.mappings;
                console.log('[Step4] 자동 매핑 생성 완료', imageMapping);
            } catch (e) {
                console.warn('[Step4] 자동 매핑 생성 실패', e);
            }
        }

        // imageMapping이 imageIndex->section 형태인 경우를 처리하여 sectionKey->[imageIndices] 로 변환
        let sectionToImages = {};
        if (imageMapping) {
            if (Array.isArray(imageMapping)) {
                // 이미 sectionKey 배열이면 그대로 사용 (예: {hero:[0], ...})
                sectionToImages = imageMapping;
            } else {
                // 객체일 때 imageIndex->sectionName 구조
                Object.entries(imageMapping).forEach(([imgIdx, sec]) => {
                    if (!sectionToImages[sec]) sectionToImages[sec] = [];
                    sectionToImages[sec].push(parseInt(imgIdx));
                });
            }
        }

        // 섹션별 이미지 HTML 반환 헬퍼
        const getImageHtml = (sectionKey) => {
            if (!sectionToImages[sectionKey] || sectionToImages[sectionKey].length === 0) return '';
            return sectionToImages[sectionKey].map(imgIdx => {
                const imgData = analyzedImages[imgIdx] || {};
                const base64 = imgData.base64 || '';
                const caption = imgData.caption || imgData.image_caption || '';
                if (!base64) return '';
                return `<div class=\"section-image\"><img src=\"${base64}\" alt=\"${this.escapeHtml(caption)}\" style=\"max-width:100%;height:auto;border-radius:8px;margin:10px 0;\"></div>`;
            }).join('');
        };
        const container = document.getElementById('generated-text-container');
        if (!container) return;

        // 간단한 렌더링 템플릿
        const { title = '', hero = '', warning = '', sevenStep = '', warranty = '', review = '', cta = '' } = sections;

        let html = '';
        if (title) html += `<h1 class="generated-title">${this.escapeHtml(title)}</h1>`;
        if (hero) html += `<h2 class=\"generated-hero\">${this.escapeHtml(hero)}</h2>${getImageHtml('hero')}`;
        if (warning) html += `<div class=\"generated-warning\"><h3>넛지 위험 경고</h3><p>${this.escapeHtml(warning).replace(/\n/g, '<br>')}</p>${getImageHtml('warning')}</div>`;
        if (sevenStep) html += `<div class=\"generated-step\"><h3>설명 섹션</h3><p>${this.escapeHtml(sevenStep).replace(/\n/g, '<br>')}</p>${getImageHtml('sevenStep')}</div>`;
        if (warranty) html += `<div class=\"generated-warranty\"><p>${this.escapeHtml(warranty).replace(/\n/g, '<br>')}</p>${getImageHtml('warranty')}</div>`;
        if (review) html += `<div class=\"generated-review\"><p>${this.escapeHtml(review).replace(/\n/g, '<br>')}</p>${getImageHtml('review')}</div>`;
        if (cta) html += `<div class=\"generated-cta\"><h3>넛지 CTA</h3><p>${this.escapeHtml(cta).replace(/\n/g, '<br>')}</p>${getImageHtml('cta')}</div>`;

        container.innerHTML = html;
        // 결과 영역이 보이도록 스크롤
        container.scrollIntoView({ behavior: 'smooth' });
    }
};

// 전역 객체로 등록
window.Step4DataDisplay = Step4DataDisplay;