/**
 * Step 4 Data Display Module
 * Session Storage에서 데이터를 가져와서 step4-content 클래스에 표시하는 모듈
 */

const Step4DataDisplay = {
    /**
     * 초기화 함수
     */
    init() {
        console.log('=== Step4DataDisplay 초기화 시작 ===');
        this.loadAndDisplaySessionData();
        this.addActionButtons();
    },

    /**
     * Session Storage에서 모든 데이터를 가져와서 표시
     */
    loadAndDisplaySessionData() {
        const container = document.querySelector('.step4-content');
        if (!container) {
            console.error('step4-content 클래스를 가진 요소를 찾을 수 없습니다.');
            return;
        }

        // Session Storage의 모든 데이터 수집
        const sessionData = this.getAllSessionStorageData();
        
        // 데이터 표시
        this.displaySessionData(container, sessionData);
    },

    /**
     * Session Storage의 모든 데이터를 수집
     */
    getAllSessionStorageData() {
        const data = {};
        
        // 모든 Session Storage 키 순회
        for (let i = 0; i < sessionStorage.length; i++) {
            const key = sessionStorage.key(i);
            try {
                const value = sessionStorage.getItem(key);
                // JSON 파싱 시도
                try {
                    data[key] = JSON.parse(value);
                } catch {
                    // JSON이 아닌 경우 문자열로 저장
                    data[key] = value;
                }
            } catch (error) {
                console.error(`Session Storage 키 '${key}' 읽기 오류:`, error);
            }
        }
        
        console.log('수집된 Session Storage 데이터:', data);
        return data;
    },

    /**
     * Session Storage 데이터를 화면에 표시 (step3_complete_data만)
     */
    displaySessionData(container, sessionData) {
        let html = '<h2>🖼️ 이미지 관련 Session Storage 데이터</h2>';
        
        // step3_complete_data 키만 표시
        const targetKey = 'step3_complete_data';
        const targetData = sessionData[targetKey];
        
        // 데이터가 없는 경우
        if (!targetData) {
            html += '<div class="no-data-message">📭 \'step3_complete_data\' Session Storage 데이터가 없습니다.</div>';
            container.innerHTML = html;
            return;
        }

        html += '<div class="session-data-container">';

        // step3_complete_data만 표시
        html += this.createDataCard(targetKey, targetData, true);

        html += '</div>';

        container.innerHTML = html;
    },

    /**
     * 개별 데이터 카드 생성
     */
    createDataCard(key, value, isPriority = false) {
        const cardClass = isPriority ? 'data-card priority-card' : 'data-card';
        const icon = this.getIconForKey(key);
        const displayName = this.getDisplayNameForKey(key);
        
        let content = '';
        
        if (typeof value === 'object' && value !== null) {
            content = this.formatObjectData(value);
        } else {
            content = `<div class="simple-value">${this.escapeHtml(String(value))}</div>`;
        }

        return `
            <div class="${cardClass}">
                <div class="card-header">
                    <h4>${icon} ${displayName}</h4>
                    <span class="key-name">(${key})</span>
                </div>
                <div class="card-content">
                    ${content}
                </div>
            </div>
        `;
    },

    /**
     * 객체 데이터를 포맷팅
     */
    formatObjectData(obj) {
        if (Array.isArray(obj)) {
            // images 배열인지 확인
            if (obj.length > 0 && obj[0].base64 && obj[0].caption) {
                return this.formatImagesArray(obj);
            }
            return `<div class="array-data">배열 (${obj.length}개 항목)</div>` + 
                   this.formatArrayPreview(obj);
        }
        
        let html = '<div class="object-data">';
        // 제외할 필드들
        const excludeFields = ['dateInfo', 'keywordInfo', 'timestamp', 'totalImages'];
        
        Object.keys(obj).filter(key => !excludeFields.includes(key)).slice(0, 5).forEach(key => {
            const value = obj[key];
            
            if (typeof value === 'object' && value !== null) {
                if (Array.isArray(value)) {
                    // images 배열인지 확인하고 실제 이미지로 표시
                    if (key === 'images' && value.length > 0 && value[0].base64) {
                        html += `<div class="object-item"><strong>${key}:</strong></div>`;
                        html += this.formatImagesArray(value);
                        return; // 다음 키로 넘어감
                    } else {
                        html += `<div class="object-item"><strong>${key}:</strong> 배열 (${value.length}개)</div>`;
                    }
                } else {
                    html += `<div class="object-item"><strong>${key}:</strong> 객체</div>`;
                }
            } else {
                const displayValue = String(value).length > 50 ? 
                    String(value).substring(0, 50) + '...' : 
                    String(value);
                html += `<div class="object-item"><strong>${key}:</strong> ${this.escapeHtml(displayValue)}</div>`;
            }
        });
        
        if (Object.keys(obj).length > 5) {
            html += `<div class="more-items">... 외 ${Object.keys(obj).length - 5}개 항목</div>`;
        }
        
        html += '</div>';
        return html;
    },

    /**
     * 이미지 배열을 깔끔하게 포맷팅
     */
    formatImagesArray(images) {
        let html = `<div class="images-array-container">`;
        html += `<div class="images-header">📷 이미지 ${images.length}개</div>`;
        
        images.forEach((image, index) => {
            html += `<div class="image-item">`;
            
            // 이미지 썸네일 (200px 너비, 비율 유지)
            if (image.base64) {
                html += `<div class="image-thumbnail-200">`;
                html += `<img src="${image.base64}" alt="${image.filename || image.name || `이미지 ${index + 1}`}" style="width: 200px; height: auto;" />`;
                html += `</div>`;
            }
            
            // 이미지 정보
            html += `<div class="image-info">`;
            
            // 파일명
            if (image.filename) {
                html += `<div class="image-filename">📄 <strong>파일명:</strong> ${this.escapeHtml(image.filename)}</div>`;
            }
            
            // 캡션
            if (image.caption) {
                html += `<div class="image-caption">💬 <strong>캡션:</strong> ${this.escapeHtml(image.caption)}</div>`;
            }
            
            // 추가 정보
            const additionalInfo = [];
            if (image.analysis) {
                additionalInfo.push('🔍 분석 완료');
            }
            if (image.edited) {
                additionalInfo.push('✏️ 편집됨');
            }
            
            if (additionalInfo.length > 0) {
                html += `<div class="image-status">${additionalInfo.join(' • ')}</div>`;
            }
            
            html += `</div>`; // image-info 끝
            html += `</div>`; // image-item 끝
        });
        
        html += `</div>`; // images-array-container 끝
        return html;
    },

    /**
     * 배열 미리보기 생성
     */
    formatArrayPreview(arr) {
        if (arr.length === 0) return '<div class="empty-array">빈 배열</div>';
        
        let html = '<div class="array-preview">';
        arr.slice(0, 3).forEach((item, index) => {
            let preview = '';
            if (typeof item === 'object' && item !== null) {
                preview = '객체';
            } else {
                preview = String(item).length > 30 ? 
                    String(item).substring(0, 30) + '...' : 
                    String(item);
            }
            html += `<div class="array-item">[${index}] ${this.escapeHtml(preview)}</div>`;
        });
        
        if (arr.length > 3) {
            html += `<div class="more-items">... 외 ${arr.length - 3}개 항목</div>`;
        }
        
        html += '</div>';
        return html;
    },

    /**
     * 키에 따른 아이콘 반환
     */
    getIconForKey(key) {
        const iconMap = {
            'step3_complete_data': '📋',
            'step3Data': '📄',
            'shilla_date_data': '📅',
            'shilla_keyword_data': '🔧',
            'shilla_image_data': '🖼️',
            'shilla_analysis_data': '🔍',
            'shilla_content_data': '📝',
            'shilla_current_step': '👣',
            'analyzedImages': '🔬',
            'generatedSections': '📑',
            'imageMapping': '🗺️',
            'editedImageUrl': '✏️'
        };
        
        return iconMap[key] || '📦';
    },

    /**
     * 키에 따른 표시명 반환
     */
    getDisplayNameForKey(key) {
        const nameMap = {
            'step3_complete_data': 'Step 3 완료 데이터',
            'step3Data': 'Step 3 데이터',
            'shilla_date_data': '날짜 데이터',
            'shilla_keyword_data': '키워드 데이터',
            'shilla_image_data': '이미지 데이터',
            'shilla_analysis_data': '분석 데이터',
            'shilla_content_data': '콘텐츠 데이터',
            'shilla_current_step': '현재 단계',
            'analyzedImages': '분석된 이미지',
            'generatedSections': '생성된 섹션',
            'imageMapping': '이미지 매핑',
            'editedImageUrl': '편집된 이미지 URL'
        };
        
        return nameMap[key] || key;
    },

    /**
     * HTML 이스케이프
     */
    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    },

    /**
     * 액션 버튼들을 step-container 하단에 추가
     */
    addActionButtons() {
        const stepContainer = document.getElementById('step-container');
        if (!stepContainer) {
            console.error('step-container를 찾을 수 없습니다.');
            return;
        }

        // 버튼 컨테이너 생성
        const buttonContainer = document.createElement('div');
        buttonContainer.className = 'action-buttons-container';
        buttonContainer.innerHTML = `
            <div class="action-buttons">
                <button id="contact-image-btn" class="action-btn primary-btn" style="display: none;">
                    📞 연락처 이미지 생성
                </button>
                <button id="text-generation-btn" class="action-btn secondary-btn">
                    📝 텍스트 생성
                </button>
            </div>
            <!-- 생성된 텍스트가 표시될 영역 -->
            <div id="generated-text-container" class="generated-text-container"></div>
        `;

        // step-container 하단에 추가
        stepContainer.appendChild(buttonContainer);

        // 이벤트 리스너 추가
        this.attachButtonEventListeners();
    },

    /**
     * 버튼 이벤트 리스너 추가
     */
    attachButtonEventListeners() {
        const contactImageBtn = document.getElementById('contact-image-btn');
        const textGenerationBtn = document.getElementById('text-generation-btn');

        if (contactImageBtn) {
            contactImageBtn.addEventListener('click', () => {
                console.log('연락처 이미지 생성 버튼 클릭');
                this.handleContactImageGeneration();
            });
        }

        if (textGenerationBtn) {
            textGenerationBtn.addEventListener('click', () => {
                console.log('텍스트 생성 버튼 클릭');
                this.handleTextGeneration();
            });
        }
    },

    /**
     * 연락처 이미지 생성 처리
     */
    handleContactImageGeneration() {
        if (window.ContactImageGenerator && typeof ContactImageGenerator.generateContactImage === 'function') {
            ContactImageGenerator.generateContactImage();
        } else {
            alert('⚠️ ContactImageGenerator 모듈을 로드하지 못했습니다.');
        }
    },

    /**
     * 상세 이미지 정보 추출
     */
    extractDetailedImageInfo(images) {
        return images.map((img, index) => {
            const imageInfo = {
                index: index,
                filename: img.filename || img.name || `image_${index + 1}`,
                caption: img.caption || '',
                base64: img.base64 || ''
            };

            // 분석 결과가 있는 경우 추가
            if (img.analysis) {
                imageInfo.analysis = {
                    success: img.analysis.success || false,
                    description: img.analysis.description || '',
                    objects: img.analysis.objects || [],
                    text: img.analysis.text || '',
                    confidence: img.analysis.confidence || 0
                };
            }

            // 추가 메타데이터
            if (img.size) imageInfo.size = img.size;
            if (img.type) imageInfo.type = img.type;
            if (img.edited) imageInfo.edited = img.edited;

            return imageInfo;
        });
    },

    /**
     * 개선된 프롬프트 빌드 (상세 이미지 정보 포함)
     */
    buildEnhancedPrompt(step3, imageDetails, initialMapping) {
        // PromptConfig의 개선된 프롬프트 사용
        return PromptConfig.getTextGenerationPrompt(step3, imageDetails, initialMapping);
    },

    /**
     * 텍스트 생성 처리
     */
    handleTextGeneration() {
        console.log('📝 텍스트 생성 로직 시작');

        // 1) 세션 데이터 확보
        let step3Raw = sessionStorage.getItem('step3_complete_data');
        if (!step3Raw) {
            alert('📭 step3_complete_data 가 없습니다. 먼저 3단계를 완료하세요.');
            return;
        }
        let step3;
        try {
            step3 = JSON.parse(step3Raw);
        } catch (e) {
            console.error('JSON 파싱 실패', e);
            alert('step3 데이터 파싱 오류');
            return;
        }

        // 2) 상세 이미지 정보 수집
        const imageDetails = this.extractDetailedImageInfo(step3.images || []);
        console.log('상세 이미지 정보:', imageDetails);

        // 3) 초기 이미지-섹션 매핑 제안 생성
        let initialMapping = null;
        if (imageDetails.length > 0 && window.ImageMapperService) {
            try {
                // 기본 섹션 구조 정의
                const basicSections = {
                    hero: { title: 'Hero 섹션' },
                    warning: { title: '위험 경고 섹션' },
                    sevenStep: { title: '설명 섹션 1' },
                    warranty: { title: '설명 섹션 2' },
                    review: { title: '고객 후기 섹션' },
                    cta: { title: 'CTA 섹션' }
                };

                const mappingResult = window.ImageMapperService.createImageMapping(
                    imageDetails,
                    basicSections,
                    { autoMapping: true }
                );
                initialMapping = mappingResult.mappings;
                console.log('초기 매핑 제안:', initialMapping);
            } catch (e) {
                console.warn('초기 매핑 생성 실패:', e);
            }
        }

        // 4) 개선된 프롬프트 빌드
        const prompt = this.buildEnhancedPrompt(step3, imageDetails, initialMapping);
        console.log('생성 프롬프트 길이:', prompt.length);
        console.log('프롬프트 샘플:', prompt.substring(0, 500) + '...');

        // 5) LLM 호출 (JSON 반환 기대)
        const systemMsg = 'You are an expert Korean blog copywriter and image curator who returns ONLY valid JSON with precise image-section mappings.';
        TextGenerationAPI.generateJsonText(prompt, systemMsg, {
            model: 'o3'
        }).then(json => {
            console.log('LLM 결과 JSON:', json);

            // 6) 이미지 매핑 정보 저장
            if (json.imageMapping) {
                sessionStorage.setItem('imageMapping', JSON.stringify({
                    mappings: json.imageMapping,
                    source: 'llm_generated',
                    timestamp: new Date().toISOString()
                }));
            }

            // 7) 생성된 섹션 저장 및 렌더링
            sessionStorage.setItem('generatedSections', JSON.stringify(json));
            this.renderGeneratedSections(json);
        }).catch(err => {
            console.error('텍스트 생성 실패', err);
            alert('❌ 텍스트 생성 실패: ' + err.message);
        });
    },



    /**
     * Step3 데이터와 이미지 캡션을 이용해 프롬프트 문자열 생성 (레거시 호환)
     */
    buildPrompt(step3, _captions = []) {
        // 레거시 호환을 위해 유지하되, 개선된 함수 사용 권장
        const imageDetails = this.extractDetailedImageInfo(step3.images || []);
        return this.buildEnhancedPrompt(step3, imageDetails, null);
    },

    /**
     * 생성된 섹션 JSON을 화면에 렌더링
     */
    renderGeneratedSections(sections) {
        console.log('🎨 섹션 렌더링 시작:', sections);

        // 이미지 데이터 확보
        const analyzedImages = this.getAnalyzedImages();
        console.log('[Step4] 확보된 이미지 개수:', analyzedImages.length);

        // 이미지 매핑 정보 확보 (우선순위: LLM 생성 > 자동 매핑 > 기본 매핑)
        let imageMapping = this.getImageMapping(sections, analyzedImages);
        console.log('[Step4] 최종 이미지 매핑:', imageMapping);

        // 섹션별 이미지 매핑 생성
        const sectionToImages = this.createSectionImageMapping(imageMapping);
        console.log('[Step4] 섹션별 이미지 매핑:', sectionToImages);

        // HTML 렌더링
        this.renderSectionsWithImages(sections, sectionToImages, analyzedImages);
    },

    /**
     * 분석된 이미지 데이터 확보
     */
    getAnalyzedImages() {
        let analyzedImages = [];

        // 1차: step3_complete_data에서 직접 확보
        try {
            const step3Raw = sessionStorage.getItem('step3_complete_data');
            if (step3Raw) {
                const step3Data = JSON.parse(step3Raw);
                if (step3Data.images && Array.isArray(step3Data.images)) {
                    analyzedImages = step3Data.images;
                    console.log('[Step4] step3_complete_data에서 이미지 확보:', analyzedImages.length);
                    return analyzedImages;
                }
            }
        } catch (e) {
            console.warn('[Step4] step3_complete_data 파싱 오류:', e);
        }

        // 2차: 다른 키들에서 fallback 탐색
        const fallbackKeys = ['analyzedImages', 'shilla_image_data', 'shilla_analysis_data'];
        for (const key of fallbackKeys) {
            try {
                const raw = sessionStorage.getItem(key);
                if (raw) {
                    const parsed = JSON.parse(raw);
                    const candidate = Array.isArray(parsed) ? parsed :
                                    (parsed.images && Array.isArray(parsed.images) ? parsed.images : null);
                    if (candidate && candidate.length > 0) {
                        analyzedImages = candidate;
                        console.log(`[Step4] ${key}에서 이미지 확보:`, analyzedImages.length);
                        break;
                    }
                }
            } catch (e) {
                console.warn(`[Step4] ${key} 파싱 오류:`, e);
            }
        }

        return analyzedImages;
    },

    /**
     * 이미지 매핑 정보 확보
     */
    getImageMapping(sections, analyzedImages) {
        // 1차: LLM이 생성한 매핑 (sections.imageMapping)
        if (sections.imageMapping && typeof sections.imageMapping === 'object') {
            console.log('[Step4] LLM 생성 매핑 사용');
            return sections.imageMapping;
        }

        // 2차: 세션 스토리지의 매핑 정보
        try {
            const mappingRaw = sessionStorage.getItem('imageMapping');
            if (mappingRaw) {
                const parsed = JSON.parse(mappingRaw);
                const mapping = parsed.mappings || parsed;
                if (mapping && Object.keys(mapping).length > 0) {
                    console.log('[Step4] 세션 스토리지 매핑 사용');
                    return mapping;
                }
            }
        } catch (e) {
            console.warn('[Step4] 세션 매핑 파싱 오류:', e);
        }

        // 3차: 자동 매핑 생성
        if (analyzedImages.length > 0 && window.ImageMapperService) {
            try {
                const basicSections = {
                    hero: { title: 'Hero 섹션' },
                    warning: { title: '위험 경고 섹션' },
                    sevenStep: { title: '설명 섹션 1' },
                    warranty: { title: '설명 섹션 2' },
                    review: { title: '고객 후기 섹션' },
                    cta: { title: 'CTA 섹션' }
                };

                const mappingResult = window.ImageMapperService.createImageMapping(
                    analyzedImages,
                    basicSections,
                    { autoMapping: true }
                );
                console.log('[Step4] 자동 매핑 생성 완료');
                return mappingResult.mappings;
            } catch (e) {
                console.warn('[Step4] 자동 매핑 생성 실패:', e);
            }
        }

        return {};
    },

    /**
     * 섹션별 이미지 매핑 생성 (imageIndex->section을 section->[imageIndices]로 변환)
     */
    createSectionImageMapping(imageMapping) {
        let sectionToImages = {};

        if (!imageMapping || Object.keys(imageMapping).length === 0) {
            return sectionToImages;
        }

        if (Array.isArray(imageMapping)) {
            // 이미 sectionKey 배열이면 그대로 사용 (예: {hero:[0], ...})
            sectionToImages = imageMapping;
        } else {
            // 객체일 때 imageIndex->sectionName 구조를 변환
            Object.entries(imageMapping).forEach(([imgIdx, sectionName]) => {
                if (!sectionToImages[sectionName]) {
                    sectionToImages[sectionName] = [];
                }
                sectionToImages[sectionName].push(parseInt(imgIdx));
            });
        }

        return sectionToImages;
    },

    /**
     * 섹션과 이미지를 함께 렌더링
     */
    renderSectionsWithImages(sections, sectionToImages, analyzedImages) {
        const container = document.getElementById('generated-text-container');
        if (!container) {
            console.error('generated-text-container를 찾을 수 없습니다.');
            return;
        }

        // 섹션별 이미지 HTML 생성 헬퍼
        const getImageHtml = (sectionKey, position = 'after') => {
            if (!sectionToImages[sectionKey] || sectionToImages[sectionKey].length === 0) {
                return '';
            }

            const images = sectionToImages[sectionKey].map(imgIdx => {
                const imgData = analyzedImages[imgIdx] || {};
                const base64 = imgData.base64 || '';
                const filename = imgData.filename || imgData.name || `이미지 ${imgIdx + 1}`;

                if (!base64) return '';

                return `
                    <div class="section-image ${position}">
                        <img src="${base64}"
                             alt="${this.escapeHtml(filename)}"
                             title="${this.escapeHtml(filename)}"
                             style="max-width:100%;height:auto;margin:15px 0;">
                    </div>
                `;
            }).join('');

            return `<div class="section-images-container">${images}</div>`;
        };

        // 섹션 데이터 추출
        const {
            title = '',
            hero = '',
            warning = '',
            sevenStep = '',
            warranty = '',
            review = '',
            cta = ''
        } = sections;

        // HTML 구성
        let html = '<div class="generated-content-wrapper">';

        // 제목
        if (title) {
            html += `<div class="content-section title-section">
                <h1 class="generated-title">${this.escapeHtml(title)}</h1>
            </div>`;
        }

        // Hero 섹션 (이미지를 상단에 배치)
        if (hero) {
            html += `<div class="content-section">
                ${getImageHtml('hero', 'before')}
                <div class="generated-hero">
                    <h2>${this.escapeHtml(hero).replace(/\n/g, '<br>')}</h2>
                </div>
            </div>`;
        }

        // Warning 섹션 (이미지를 중간에 배치)
        if (warning) {
            const warningParts = warning.split('\n\n');
            const firstPart = warningParts[0] || warning;
            const restParts = warningParts.slice(1).join('\n\n');

            html += `<div class="content-section">
                <h3>⚠️ 주의사항</h3>
                <div class="warning-content">
                    <p>${this.escapeHtml(firstPart).replace(/\n/g, '<br>')}</p>
                    ${getImageHtml('warning', 'middle')}
                    ${restParts ? `<p>${this.escapeHtml(restParts).replace(/\n/g, '<br>')}</p>` : ''}
                </div>
            </div>`;
        }

        // SevenStep 섹션 (이미지를 내용 사이에 배치)
        if (sevenStep) {
            html += `<div class="content-section">
                <h3>🔧 전문 설치 과정</h3>
                <div class="step-content">
                    <p>${this.escapeHtml(sevenStep).replace(/\n/g, '<br>')}</p>
                    ${getImageHtml('sevenStep', 'after')}
                </div>
            </div>`;
        }

        // Warranty 섹션
        if (warranty) {
            html += `<div class="content-section">
                <h3>✅ 품질 보증</h3>
                <div class="warranty-content">
                    <p>${this.escapeHtml(warranty).replace(/\n/g, '<br>')}</p>
                    ${getImageHtml('warranty', 'after')}
                </div>
            </div>`;
        }

        // Review 섹션
        if (review) {
            html += `<div class="content-section">
                <h3>💬 고객 후기</h3>
                <div class="review-content">
                    <p>${this.escapeHtml(review).replace(/\n/g, '<br>')}</p>
                    ${getImageHtml('review', 'after')}
                </div>
            </div>`;
        }

        // CTA 섹션 (이미지를 하단에 배치)
        if (cta) {
            html += `<div class="content-section">
                <h3>📞 지금 바로 문의하세요!</h3>
                <div class="cta-content">
                    <p>${this.escapeHtml(cta).replace(/\n/g, '<br>')}</p>
                </div>
                ${getImageHtml('cta', 'after')}
            </div>`;
        }

        html += '</div>';

        // 렌더링 및 스타일 적용
        container.innerHTML = html;
        this.applySectionStyles();

        // 결과 영역이 보이도록 스크롤
        container.scrollIntoView({ behavior: 'smooth' });

        console.log('✅ 섹션 렌더링 완료');
    },

    /**
     * 생성된 콘텐츠에 스타일 적용
     */
    applySectionStyles() {
        const style = document.createElement('style');
        style.textContent = `
            .generated-content-wrapper {
                max-width: 800px;
                margin: 20px auto;
                padding: 20px;
                background: #fff;
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            }
            .content-section {
                margin-bottom: 40px;
                padding: 0;
            }
            .content-section:last-child {
                margin-bottom: 0;
            }
            .content-section h1 {
                color: #333;
                margin: 0 0 20px 0;
                font-size: 28px;
                font-weight: 700;
                line-height: 1.3;
            }
            .content-section h2 {
                color: #333;
                margin: 0 0 20px 0;
                font-size: 24px;
                font-weight: 600;
                line-height: 1.4;
            }
            .content-section h3 {
                color: #333;
                margin: 0 0 15px 0;
                font-size: 20px;
                font-weight: 600;
                line-height: 1.4;
            }
            .content-section p {
                line-height: 1.7;
                color: #444;
                margin: 0 0 20px 0;
                font-size: 16px;
            }
            .section-images-container {
                margin: 20px 0;
            }
            .section-image {
                text-align: center;
                margin: 20px 0;
            }
        `;

        // 기존 스타일이 있다면 제거
        const existingStyle = document.getElementById('generated-content-styles');
        if (existingStyle) {
            existingStyle.remove();
        }

        style.id = 'generated-content-styles';
        document.head.appendChild(style);
    }
};

// 전역 객체로 등록
window.Step4DataDisplay = Step4DataDisplay;