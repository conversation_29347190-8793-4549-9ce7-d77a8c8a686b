// 페이지 네비게이션 관리 시스템
const Navigation = {
    // 페이지 정의
    PAGES: {
        1: 'step1.html',
        2: 'step2.html',
        3: 'step3.html',
        4: 'step4.html'
    },

    STEP_NAMES: {
        1: '날짜 선택',
        2: '키워드 입력',
        3: '이미지 업로드',
        4: '이미지 분석'
    },

    // 현재 페이지의 단계 번호 가져오기
    getCurrentStepFromURL() {
        const path = window.location.pathname;
        const filename = path.split('/').pop();
        
        for (const [step, page] of Object.entries(this.PAGES)) {
            if (filename === page) {
                return parseInt(step);
            }
        }
        return 1; // 기본값
    },

    // 특정 단계로 이동
    goToStep(step) {
        if (step >= 1 && step <= 4) {
            StateManager.setCurrentStep(step);
            window.location.href = this.PAGES[step];
        } else {
            console.error('Navigation: Invalid step number', step);
        }
    },

    // 다음 단계로 이동
    nextStep() {
        const currentStep = this.getCurrentStepFromURL();

        // Step 3에서 Step 4로 이동할 때 모든 데이터를 Session Storage에 저장
        if (currentStep === 3) {
            this.saveAllDataToSessionStorage();
        }

        // Step 4에서 다음 버튼 클릭 시 블로그 콘텐츠 다운로드
        if (currentStep === 4) {
            this.downloadBlogContent();
            return; // 다운로드 후 페이지 이동하지 않음
        }

        if (currentStep < 4) {
            this.goToStep(currentStep + 1);
        }
    },

    // 이전 단계로 이동
    prevStep() {
        const currentStep = this.getCurrentStepFromURL();
        if (currentStep > 1) {
            this.goToStep(currentStep - 1);
        }
    },

    // 페이지 초기화 (공통 설정)
    initPage() {
        const currentStep = this.getCurrentStepFromURL();
        StateManager.setCurrentStep(currentStep);
        
        // 단계 표시기 업데이트
        this.updateStepIndicator();
        
        // 네비게이션 버튼 설정
        this.setupNavigationButtons();
        
        // 진행률 표시
        this.updateProgress();
        
        console.log(`Navigation: Initialized page for step ${currentStep}`);
    },

    // 단계 표시기 업데이트
    updateStepIndicator() {
        const currentStep = this.getCurrentStepFromURL();
        const currentStepEl = document.getElementById('current-step');
        const totalStepsEl = document.getElementById('total-steps');
        const stepNameEl = document.getElementById('step-name');
        
        if (currentStepEl) currentStepEl.textContent = currentStep;
        if (totalStepsEl) totalStepsEl.textContent = '4';
        if (stepNameEl) stepNameEl.textContent = this.STEP_NAMES[currentStep];
    },

    // 네비게이션 버튼 설정
    setupNavigationButtons() {
        const currentStep = this.getCurrentStepFromURL();
        const prevBtn = document.getElementById('prev-btn');
        const nextBtn = document.getElementById('next-btn');
        
        if (prevBtn) {
            prevBtn.disabled = currentStep === 1;
            prevBtn.onclick = () => this.prevStep();
        }
        
        if (nextBtn) {
            if (currentStep === 4) {
                nextBtn.style.display = 'block';
                nextBtn.innerHTML = '📥 다운로드';
                nextBtn.onclick = () => {
                    if (this.validateCurrentStep()) {
                        this.nextStep();
                    } else {
                        alert('현재 단계를 완료해주세요.');
                    }
                };
            } else if (currentStep < 4) {
                nextBtn.style.display = 'block';
                nextBtn.textContent = '다음';
                nextBtn.onclick = () => {
                    if (this.validateCurrentStep()) {
                        this.nextStep();
                    } else {
                        alert('현재 단계를 완료해주세요.');
                    }
                };
            } else {
                nextBtn.style.display = 'none';
            }
        }
    },

    // 현재 단계 검증
    validateCurrentStep() {
        const currentStep = this.getCurrentStepFromURL();
        
        // 각 단계별 검증 로직
        switch (currentStep) {
            case 1:
                return window.DateSelector ? window.DateSelector.validate() : true;
            case 2:
                return window.KeywordInput ? window.KeywordInput.validate() : true;
            case 3:
                return window.ImageUpload ? window.ImageUpload.validate() : true;
            case 4:
                return window.ImageAnalysis ? window.ImageAnalysis.validate() : true;
            case 5:
                return window.ContentEditor ? window.ContentEditor.validate() : true;
            case 6:
                return true; // 마지막 단계는 항상 유효
            default:
                return true;
        }
    },

    // 진행률 업데이트
    updateProgress() {
        const progress = StateManager.getProgress();
        const progressBar = document.getElementById('progress-bar');
        const progressText = document.getElementById('progress-text');
        
        if (progressBar) {
            progressBar.style.width = `${progress}%`;
        }
        
        if (progressText) {
            progressText.textContent = `${progress}%`;
        }
    },

    // 브레드크럼 생성
    createBreadcrumb() {
        const currentStep = this.getCurrentStepFromURL();
        const breadcrumbEl = document.getElementById('breadcrumb');
        
        if (!breadcrumbEl) return;
        
        let breadcrumbHTML = '';
        for (let i = 1; i <= 6; i++) {
            const isActive = i === currentStep;
            const isCompleted = i < currentStep;
            const className = isActive ? 'active' : (isCompleted ? 'completed' : 'pending');
            
            breadcrumbHTML += `
                <span class="breadcrumb-item ${className}" onclick="Navigation.goToStep(${i})">
                    ${i}. ${this.STEP_NAMES[i]}
                </span>
            `;
        }
        
        breadcrumbEl.innerHTML = breadcrumbHTML;
    },

    // 데이터 백업 및 복원
    backupCurrentStepData() {
        const currentStep = this.getCurrentStepFromURL();
        
        switch (currentStep) {
            case 1:
                if (window.DateSelector) {
                    StateManager.saveDateData(window.DateSelector.getData());
                }
                break;
            case 2:
                if (window.KeywordInput) {
                    StateManager.saveKeywordData(window.KeywordInput.getData());
                }
                break;
            case 3:
                if (window.ImageUpload) {
                    StateManager.saveImageData(window.ImageUpload.getData());
                }
                break;
            case 4:
                if (window.ImageAnalysis) {
                    StateManager.saveAnalysisData(window.ImageAnalysis.getData());
                }
                break;
            case 5:
                if (window.ContentEditor) {
                    StateManager.saveContentData(window.ContentEditor.getData());
                }
                break;
        }
    },

    // Step 3에서 Step 4로 이동할 때 분석 완료된 이미지 데이터를 Session Storage에 저장
    saveAllDataToSessionStorage() {
        console.log('=== saveAllDataToSessionStorage 함수 호출됨 ===');
        
        try {
            // 기존 데이터 삭제 (덮어쓰기 보장)
            sessionStorage.removeItem('step3_complete_data');
            console.log('기존 step3_complete_data 삭제 완료');
            
            // 1. 업로드된 이미지와 분석 결과 수집
            console.log('ImageUpload 객체 확인:', window.ImageUpload);
            const imageData = window.ImageUpload ? window.ImageUpload.getData() : null;
            console.log('📷 수집된 이미지 데이터:', imageData);
            
            // ImageUpload.images 배열 직접 확인
            if (window.ImageUpload && window.ImageUpload.images) {
                console.log('🔍 ImageUpload.images 배열:', window.ImageUpload.images);
                console.log('📊 ImageUpload.validate() 결과:', window.ImageUpload.validate());
            }
            
            // 2. 분석 완료된 이미지만 필터링하여 base64와 caption 추출
            let processedImages = [];
            if (imageData && imageData.images) {
                console.log('🔎 필터링 전 이미지 개수:', imageData.images.length);
                
                processedImages = imageData.images
                    .filter(image => {
                        const hasAnalysis = image.analysis && image.analysis.success && image.analysis.image_caption;
                        console.log(`이미지 ${image.name} 필터링 결과:`, {
                            hasAnalysis: hasAnalysis,
                            analysis: image.analysis
                        });
                        return hasAnalysis;
                    })
                    .map(image => ({
                        name: image.name,
                        base64: image.base64,
                        caption: image.analysis.image_caption,
                        filename: image.analysis.filename || image.name
                    }));
            }
            
            console.log(`🔍 분석 완료된 이미지 ${processedImages.length}개 처리됨:`, processedImages);
            
            // 3. 작업 날짜 데이터 수집
            const dateData = StateManager.loadDateData();
            console.log('수집된 날짜 데이터:', dateData);
            
            // 4. 키워드 정보 데이터 수집
            const keywordData = StateManager.loadKeywordData();
            console.log('수집된 키워드 데이터:', keywordData);
            
            // 5. 통합 데이터 객체 생성
            const allData = {
                images: processedImages,
                dateInfo: dateData,
                keywordInfo: keywordData,
                timestamp: new Date().toISOString(),
                totalImages: processedImages.length
            };
            
            console.log('생성된 통합 데이터:', allData);
            
            // 6. Session Storage에 저장
            const dataString = JSON.stringify(allData);
            console.log('JSON 문자열 길이:', dataString.length);
            sessionStorage.setItem('step3_complete_data', dataString);
            console.log('Session Storage에 저장 완료');
            
            // 7. 저장 확인
            const savedData = sessionStorage.getItem('step3_complete_data');
            if (savedData) {
                const parsedData = JSON.parse(savedData);
                console.log(`✅ Step 3 데이터가 Session Storage에 저장되었습니다. 분석 완료된 이미지 ${parsedData.totalImages}개:`, parsedData);
                
                // Session Storage 전체 상태 출력
                console.log('=== 현재 Session Storage 전체 상태 ===');
                for (let i = 0; i < sessionStorage.length; i++) {
                    const key = sessionStorage.key(i);
                    const value = sessionStorage.getItem(key);
                    console.log(`${key}:`, value.substring(0, 200) + (value.length > 200 ? '...' : ''));
                }
            } else {
                console.error('❌ Session Storage 저장 실패');
            }
            
        } catch (error) {
            console.error('❌ Session Storage 저장 중 오류 발생:', error);
        }
    },

    // 페이지 언로드 시 데이터 백업
    setupAutoBackup() {
        window.addEventListener('beforeunload', () => {
            this.backupCurrentStepData();
        });
        
        // 주기적 백업 (30초마다)
        setInterval(() => {
            this.backupCurrentStepData();
        }, 30000);
    },

    // 블로그 콘텐츠 다운로드
    downloadBlogContent() {
        console.log('Navigation: 블로그 콘텐츠 다운로드 시작');

        try {
            // 생성된 콘텐츠 컨테이너 확인
            const contentContainer = document.getElementById('generated-text-container');
            if (!contentContainer || !contentContainer.innerHTML.trim()) {
                alert('⚠️ 다운로드할 콘텐츠가 없습니다.\n먼저 "📝 텍스트 생성" 버튼을 클릭하여 콘텐츠를 생성해주세요.');
                return;
            }

            // 현재 날짜로 파일명 생성
            const now = new Date();
            const dateStr = now.getFullYear() +
                          String(now.getMonth() + 1).padStart(2, '0') +
                          String(now.getDate()).padStart(2, '0');

            // HTML 파일 다운로드 (1순위)
            this.downloadAsHTML(contentContainer, dateStr);

        } catch (error) {
            console.error('❌ 블로그 콘텐츠 다운로드 오류:', error);
            alert('❌ 다운로드 중 오류가 발생했습니다. 다시 시도해주세요.');
        }
    },

    // HTML 파일로 다운로드
    downloadAsHTML(contentContainer, dateStr) {
        try {
            // HTML 문서 구조 생성
            const htmlContent = `<!DOCTYPE html>
<html lang="ko">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>신라 에어컨 블로그 콘텐츠</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 800px;
            margin: 20px auto;
            padding: 20px;
            line-height: 1.7;
            color: #444;
        }
        h1, h2, h3 {
            color: #333;
            margin: 25px 0 20px 0;
        }
        p {
            margin: 0 0 20px 0;
            font-size: 16px;
        }
        .content-section {
            margin-bottom: 40px;
        }
        .section-image {
            text-align: center;
            margin: 20px 0;
        }
        .section-image img {
            max-width: 100%;
            height: auto;
            border-radius: 8px;
        }
        .generated-title {
            font-size: 28px;
            font-weight: 700;
            margin: 25px 0 30px 0;
        }
    </style>
</head>
<body>
    <div class="blog-content">
        ${contentContainer.innerHTML}
    </div>
    <footer style="margin-top: 50px; padding-top: 20px; border-top: 1px solid #eee; text-align: center; color: #888; font-size: 14px;">
        <p>신라 에어컨 블로그 콘텐츠 - 생성일: ${new Date().toLocaleDateString('ko-KR')}</p>
    </footer>
</body>
</html>`;

            // Blob 생성 및 다운로드
            const blob = new Blob([htmlContent], { type: 'text/html;charset=utf-8' });
            const filename = `신라에어컨_블로그콘텐츠_${dateStr}.html`;

            this.triggerDownload(blob, filename);

            // 성공 메시지와 함께 텍스트 파일 다운로드 옵션 제공
            setTimeout(() => {
                if (confirm('✅ HTML 파일 다운로드가 완료되었습니다!\n\n📄 텍스트 파일(.txt)로도 다운로드하시겠습니까?')) {
                    this.downloadAsText(contentContainer, dateStr);
                }
            }, 500);

        } catch (error) {
            console.error('❌ HTML 다운로드 오류:', error);
            // HTML 다운로드 실패 시 텍스트 파일로 대체
            this.downloadAsText(contentContainer, dateStr);
        }
    },

    // 텍스트 파일로 다운로드
    downloadAsText(contentContainer, dateStr) {
        try {
            // HTML 태그 제거하고 순수 텍스트만 추출
            const tempDiv = document.createElement('div');
            tempDiv.innerHTML = contentContainer.innerHTML;

            // 이미지 태그 제거
            const images = tempDiv.querySelectorAll('img');
            images.forEach(img => img.remove());

            // 텍스트 추출 및 정리
            let textContent = tempDiv.textContent || tempDiv.innerText || '';

            // 여러 줄바꿈을 두 줄바꿈으로 정리
            textContent = textContent.replace(/\n\s*\n\s*\n/g, '\n\n');
            textContent = textContent.trim();

            // 헤더 추가
            const header = `신라 에어컨 블로그 콘텐츠
생성일: ${new Date().toLocaleDateString('ko-KR')}
========================================

`;

            const finalContent = header + textContent + '\n\n========================================\n신라 에어컨';

            // Blob 생성 및 다운로드
            const blob = new Blob([finalContent], { type: 'text/plain;charset=utf-8' });
            const filename = `신라에어컨_블로그콘텐츠_${dateStr}.txt`;

            this.triggerDownload(blob, filename);

        } catch (error) {
            console.error('❌ 텍스트 다운로드 오류:', error);
            alert('❌ 텍스트 파일 다운로드에 실패했습니다.');
        }
    },

    // 다운로드 트리거 (브라우저 호환성 고려)
    triggerDownload(blob, filename) {
        try {
            // 최신 브라우저 지원
            if (window.navigator && window.navigator.msSaveOrOpenBlob) {
                // IE 지원
                window.navigator.msSaveOrOpenBlob(blob, filename);
            } else {
                // 표준 브라우저
                const url = URL.createObjectURL(blob);
                const link = document.createElement('a');
                link.href = url;
                link.download = filename;
                link.style.display = 'none';

                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);

                // 메모리 정리
                setTimeout(() => URL.revokeObjectURL(url), 100);
            }

            console.log('✅ 파일 다운로드 완료:', filename);

        } catch (error) {
            console.error('❌ 다운로드 트리거 오류:', error);
            // 최후의 수단: 새 창에서 열기
            const url = URL.createObjectURL(blob);
            window.open(url, '_blank');
        }
    }
};

// 전역 스코프에서 접근 가능하도록 설정
window.Navigation = Navigation;

// 페이지 로드 시 자동 초기화
document.addEventListener('DOMContentLoaded', () => {
    Navigation.initPage();
    Navigation.setupAutoBackup();
});