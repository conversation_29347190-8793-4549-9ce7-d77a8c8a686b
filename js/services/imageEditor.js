// 이미지 편집 서비스 모듈
const ImageEditorService = {
    /**
     * 이미지 편집 실행
     * @param {string} base64Image - 편집할 이미지 (base64)
     * @param {Object} options - 편집 옵션
     * @param {string} options.promptType - 프롬프트 타입 ('default', 'advertising', 'professional', 'modern')
     * @param {string} options.customPrompt - 커스텀 프롬프트 (선택사항)
     * @param {string} options.model - 사용할 모델 ('dall-e-2', 'gpt-image-1')
     * @param {string} options.size - 이미지 크기
     * @param {Function} options.onProgress - 진행상황 콜백
     * @returns {Promise<string|null>} 편집된 이미지 data URL
     */
    async editImage(base64Image, options = {}) {
        console.log('🎨 이미지 편집 서비스 시작');
        
        try {
            // 진행상황 콜백 호출
            if (options.onProgress) {
                options.onProgress('이미지 편집 준비 중...');
            }

            // 프롬프트 결정
            const prompt = this.getEditPrompt(options.promptType, options.customPrompt);
            console.log('사용할 프롬프트:', prompt);

            // 이미지 유효성 검증
            this.validateImage(base64Image);

            if (options.onProgress) {
                options.onProgress('OpenAI API 호출 중...');
            }

            // OpenAIImageEditor 인스턴스 생성
            const apiKey = APIConfig.getApiKey();
            if (!apiKey) {
                throw new Error('OpenAI API 키가 설정되지 않았습니다.');
            }
            
            const imageEditor = new OpenAIImageEditor(apiKey);

            // 이미지 편집 API 호출 (수정된 image-edit-api.js 사용)
            const editedImageBase64 = await imageEditor.editImage(base64Image, prompt, {
                model: options.model || 'dall-e-2',
                size: options.size || '1024x1024',
                n: 1
            });

            if (options.onProgress) {
                options.onProgress('편집된 이미지 처리 중...');
            }

            if (!editedImageBase64) {
                throw new Error('편집된 이미지를 가져올 수 없습니다.');
            }

            // base64 데이터를 data URL로 변환
            const editedImageUrl = `data:image/png;base64,${editedImageBase64}`;

            if (options.onProgress) {
                options.onProgress('이미지 편집 완료!');
            }

            console.log('✅ 이미지 편집 서비스 완료');
            return editedImageUrl;

        } catch (error) {
            console.error('❌ 이미지 편집 서비스 오류:', error);
            
            if (options.onProgress) {
                options.onProgress(`편집 실패: ${error.message}`);
            }
            
            throw error;
        }
    },

    /**
     * 편집 프롬프트 가져오기
     * @param {string} promptType - 프롬프트 타입
     * @param {string} customPrompt - 커스텀 프롬프트
     * @returns {string} 최종 프롬프트
     */
    getEditPrompt(promptType = 'default', customPrompt = null) {
        if (customPrompt && customPrompt.trim()) {
            console.log('커스텀 프롬프트 사용');
            return customPrompt.trim();
        }
        
        const prompt = PromptConfig.getImageEditPrompt(promptType);
        console.log('프리셋 프롬프트 사용:', promptType);
        return prompt;
    },

    /**
     * 이미지 유효성 검증
     * @param {string} base64Image - 검증할 이미지
     * @throws {Error} 유효하지 않은 이미지인 경우
     */
    validateImage(base64Image) {
        if (!base64Image) {
            throw new Error('이미지 데이터가 없습니다.');
        }

        if (typeof base64Image !== 'string') {
            throw new Error('이미지 데이터가 문자열이 아닙니다.');
        }

        if (!base64Image.startsWith('data:image/')) {
            throw new Error('유효하지 않은 이미지 형식입니다. data:image/ 형식이어야 합니다.');
        }

        if (base64Image.length < 100) {
            throw new Error('이미지 데이터가 너무 짧습니다.');
        }

        console.log('✅ 이미지 유효성 검증 통과');
    },

    /**
     * 지원되는 모델 목록 가져오기
     * @returns {Array} 모델 목록
     */
    getSupportedModels() {
        return Object.keys(APIConfig.imageModels).map(modelName => ({
            name: modelName,
            displayName: this.getModelDisplayName(modelName),
            config: APIConfig.getImageModelConfig(modelName)
        }));
    },

    /**
     * 모델 표시명 가져오기
     * @param {string} modelName - 모델명
     * @returns {string} 표시명
     */
    getModelDisplayName(modelName) {
        const displayNames = {
            'dall-e-2': 'DALL-E 2 (안정적, URL 응답)',
            'gpt-image-1': 'GPT Image 1 (새로운, Base64 응답)'
        };
        return displayNames[modelName] || modelName;
    },

    /**
     * 프롬프트 타입 목록 가져오기
     * @returns {Array} 프롬프트 타입 목록
     */
    getPromptTypes() {
        return [
            {
                type: 'default',
                name: '기본',
                description: '간단한 외곽 장식',
                prompt: PromptConfig.getImageEditPrompt('default')
            },
            {
                type: 'advertising',
                name: '광고용',
                description: '텍스트 보존하며 외곽만 장식',
                prompt: PromptConfig.getImageEditPrompt('advertising')
            },
            {
                type: 'professional',
                name: '전문적',
                description: '깔끔하고 고급스러운 느낌',
                prompt: PromptConfig.getImageEditPrompt('professional')
            },
            {
                type: 'modern',
                name: '모던',
                description: '현대적이고 세련된 디자인',
                prompt: PromptConfig.getImageEditPrompt('modern')
            }
        ];
    },

    /**
     * 이미지 편집 미리보기 (실제 API 호출 없이 프롬프트만 확인)
     * @param {string} promptType - 프롬프트 타입
     * @param {string} customPrompt - 커스텀 프롬프트
     * @returns {Object} 미리보기 정보
     */
    previewEdit(promptType = 'default', customPrompt = null) {
        const prompt = this.getEditPrompt(promptType, customPrompt);
        const modelConfig = APIConfig.getImageModelConfig(APIConfig.defaults.imageModel);
        
        return {
            prompt: prompt,
            model: modelConfig.name,
            size: modelConfig.defaultSize,
            supportsResponseFormat: modelConfig.supportsResponseFormat,
            estimatedTime: this.estimateEditTime(modelConfig.name)
        };
    },

    /**
     * 편집 시간 추정
     * @param {string} modelName - 모델명
     * @returns {string} 추정 시간
     */
    estimateEditTime(modelName) {
        const times = {
            'dall-e-2': '30-60초',
            'gpt-image-1': '20-40초'
        };
        return times[modelName] || '30-60초';
    },

    /**
     * 편집 히스토리 관리 (향후 구현용)
     * @param {Object} editInfo - 편집 정보
     */
    saveEditHistory(editInfo) {
        // 향후 편집 히스토리 저장 기능 구현
        console.log('편집 히스토리 저장 (향후 구현):', editInfo);
    }
};

// 전역 스코프에 추가
window.ImageEditorService = ImageEditorService;