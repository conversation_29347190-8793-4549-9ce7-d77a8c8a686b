// 이미지 매핑 서비스 모듈
const ImageMapperService = {
    /**
     * 이미지와 섹션 매핑 생성
     * @param {Array} analyzedImages - 분석된 이미지 목록
     * @param {Object} sections - 생성된 섹션
     * @param {Object} options - 매핑 옵션
     * @param {boolean} options.autoMapping - 자동 매핑 여부
     * @param {Object} options.manualMappings - 수동 매핑 설정
     * @returns {Object} 이미지 매핑 결과
     */
    createImageMapping(analyzedImages, sections, options = {}) {
        console.log('🗺️ 이미지 매핑 서비스 시작');
        
        try {
            // 입력 데이터 유효성 검증
            this.validateMappingData(analyzedImages, sections);

            // 매핑 결과 초기화
            const mappingResult = {
                mappings: {},
                suggestions: {},
                unmappedImages: [],
                unmappedSections: [],
                confidence: {}
            };

            // 수동 매핑이 있는 경우 우선 적용
            if (options.manualMappings) {
                this.applyManualMappings(mappingResult, options.manualMappings, analyzedImages, sections);
            }

            // 자동 매핑 실행
            if (options.autoMapping !== false) {
                this.performAutoMapping(mappingResult, analyzedImages, sections);
            }

            // 매핑되지 않은 항목 처리
            this.handleUnmappedItems(mappingResult, analyzedImages, sections);

            // 매핑 품질 평가
            this.evaluateMappingQuality(mappingResult);

            console.log('✅ 이미지 매핑 서비스 완료');
            return mappingResult;

        } catch (error) {
            console.error('❌ 이미지 매핑 서비스 오류:', error);
            throw error;
        }
    },

    /**
     * 매핑 데이터 유효성 검증
     * @param {Array} analyzedImages - 분석된 이미지
     * @param {Object} sections - 섹션 데이터
     * @throws {Error} 유효하지 않은 데이터인 경우
     */
    validateMappingData(analyzedImages, sections) {
        if (!analyzedImages || !Array.isArray(analyzedImages)) {
            throw new Error('분석된 이미지 데이터가 유효하지 않습니다.');
        }

        if (!sections || typeof sections !== 'object') {
            throw new Error('섹션 데이터가 유효하지 않습니다.');
        }

        if (analyzedImages.length === 0) {
            throw new Error('분석된 이미지가 없습니다.');
        }

        if (Object.keys(sections).length === 0) {
            throw new Error('섹션이 없습니다.');
        }

        console.log('✅ 매핑 데이터 유효성 검증 통과');
    },

    /**
     * 수동 매핑 적용
     * @param {Object} mappingResult - 매핑 결과 객체
     * @param {Object} manualMappings - 수동 매핑 설정
     * @param {Array} analyzedImages - 분석된 이미지
     * @param {Object} sections - 섹션 데이터
     */
    applyManualMappings(mappingResult, manualMappings, analyzedImages, sections) {
        console.log('수동 매핑 적용 중...');
        
        for (const [imageIndex, sectionName] of Object.entries(manualMappings)) {
            const imageIdx = parseInt(imageIndex);
            
            if (imageIdx >= 0 && imageIdx < analyzedImages.length && sections[sectionName]) {
                mappingResult.mappings[imageIdx] = sectionName;
                mappingResult.confidence[imageIdx] = 1.0; // 수동 매핑은 100% 신뢰도
                console.log(`수동 매핑: 이미지 ${imageIdx} -> ${sectionName}`);
            }
        }
    },

    /**
     * 자동 매핑 실행
     * @param {Object} mappingResult - 매핑 결과 객체
     * @param {Array} analyzedImages - 분석된 이미지
     * @param {Object} sections - 섹션 데이터
     */
    performAutoMapping(mappingResult, analyzedImages, sections) {
        console.log('자동 매핑 실행 중...');
        
        const sectionNames = Object.keys(sections);
        
        analyzedImages.forEach((image, imageIndex) => {
            // 이미 수동 매핑된 이미지는 건너뛰기
            if (mappingResult.mappings.hasOwnProperty(imageIndex)) {
                return;
            }

            // 이미지 캡션 기반 섹션 추천
            const suggestions = this.suggestSectionsForImage(image, sectionNames);
            
            if (suggestions.length > 0) {
                const bestMatch = suggestions[0];
                
                // 이미 사용된 섹션이 아닌 경우에만 매핑
                if (!Object.values(mappingResult.mappings).includes(bestMatch.section)) {
                    mappingResult.mappings[imageIndex] = bestMatch.section;
                    mappingResult.confidence[imageIndex] = bestMatch.confidence;
                    console.log(`자동 매핑: 이미지 ${imageIndex} -> ${bestMatch.section} (신뢰도: ${bestMatch.confidence})`);
                } else {
                    // 이미 사용된 섹션인 경우 제안으로 저장
                    mappingResult.suggestions[imageIndex] = suggestions;
                }
            } else {
                mappingResult.suggestions[imageIndex] = [];
            }
        });
    },

    /**
     * 이미지에 대한 섹션 추천
     * @param {Object} image - 분석된 이미지
     * @param {Array} sectionNames - 섹션명 목록
     * @returns {Array} 추천 섹션 목록 (신뢰도 순)
     */
    suggestSectionsForImage(image, sectionNames) {
        if (!image.caption) {
            return [];
        }

        const caption = image.caption.toLowerCase();
        const suggestions = [];

        // 섹션별 키워드 매핑
        const sectionKeywords = {
            hero: ['제품', 'product', '메인', 'main', '대표', '로고', 'logo', '브랜드', 'brand'],
            warning: ['주의', 'warning', '경고', '안전', 'safety', '위험', 'danger', '조심'],
            sevenStep: ['단계', 'step', '과정', 'process', '방법', 'method', '설치', 'install', '순서'],
            warranty: ['보증', 'warranty', '품질', 'quality', '인증', 'certification', '보장', 'guarantee'],
            review: ['리뷰', 'review', '후기', '평가', 'rating', '고객', 'customer', '사용자', 'user'],
            cta: ['연락', 'contact', '문의', 'inquiry', '전화', 'phone', '구매', 'buy', '주문', 'order']
        };

        // 각 섹션에 대해 매칭 점수 계산
        sectionNames.forEach(sectionName => {
            const keywords = sectionKeywords[sectionName] || [];
            let score = 0;
            let matchedKeywords = [];

            keywords.forEach(keyword => {
                if (caption.includes(keyword)) {
                    score += 1;
                    matchedKeywords.push(keyword);
                }
            });

            // 추가 점수 계산 (이미지 타입, 크기 등 고려)
            if (image.type && this.isImageTypeMatchingSection(image.type, sectionName)) {
                score += 0.5;
            }

            if (score > 0) {
                const confidence = Math.min(score / keywords.length, 1.0);
                suggestions.push({
                    section: sectionName,
                    confidence: confidence,
                    matchedKeywords: matchedKeywords,
                    score: score
                });
            }
        });

        // 신뢰도 순으로 정렬
        return suggestions.sort((a, b) => b.confidence - a.confidence);
    },

    /**
     * 이미지 타입과 섹션 매칭 여부 확인
     * @param {string} imageType - 이미지 타입
     * @param {string} sectionName - 섹션명
     * @returns {boolean} 매칭 여부
     */
    isImageTypeMatchingSection(imageType, sectionName) {
        const typeMatching = {
            'product': ['hero', 'warranty'],
            'logo': ['hero', 'cta'],
            'diagram': ['sevenStep', 'warning'],
            'certificate': ['warranty'],
            'contact': ['cta'],
            'review': ['review']
        };

        return typeMatching[imageType]?.includes(sectionName) || false;
    },

    /**
     * 매핑되지 않은 항목 처리
     * @param {Object} mappingResult - 매핑 결과 객체
     * @param {Array} analyzedImages - 분석된 이미지
     * @param {Object} sections - 섹션 데이터
     */
    handleUnmappedItems(mappingResult, analyzedImages, sections) {
        // 매핑되지 않은 이미지 찾기
        analyzedImages.forEach((image, index) => {
            if (!mappingResult.mappings.hasOwnProperty(index)) {
                mappingResult.unmappedImages.push({
                    index: index,
                    image: image,
                    suggestions: mappingResult.suggestions[index] || []
                });
            }
        });

        // 매핑되지 않은 섹션 찾기
        const mappedSections = Object.values(mappingResult.mappings);
        Object.keys(sections).forEach(sectionName => {
            if (!mappedSections.includes(sectionName)) {
                mappingResult.unmappedSections.push(sectionName);
            }
        });

        console.log(`매핑되지 않은 이미지: ${mappingResult.unmappedImages.length}개`);
        console.log(`매핑되지 않은 섹션: ${mappingResult.unmappedSections.length}개`);
    },

    /**
     * 매핑 품질 평가
     * @param {Object} mappingResult - 매핑 결과 객체
     */
    evaluateMappingQuality(mappingResult) {
        const totalMappings = Object.keys(mappingResult.mappings).length;
        const highConfidenceMappings = Object.values(mappingResult.confidence)
            .filter(conf => conf >= 0.7).length;
        
        const qualityScore = totalMappings > 0 ? highConfidenceMappings / totalMappings : 0;
        
        mappingResult.qualityScore = qualityScore;
        mappingResult.qualityLevel = this.getQualityLevel(qualityScore);
        
        console.log(`매핑 품질: ${mappingResult.qualityLevel} (점수: ${qualityScore.toFixed(2)})`);
    },

    /**
     * 품질 레벨 결정
     * @param {number} score - 품질 점수
     * @returns {string} 품질 레벨
     */
    getQualityLevel(score) {
        if (score >= 0.8) return '우수';
        if (score >= 0.6) return '양호';
        if (score >= 0.4) return '보통';
        return '개선 필요';
    },

    /**
     * 매핑 결과를 사용자 친화적 형태로 변환
     * @param {Object} mappingResult - 매핑 결과
     * @param {Array} analyzedImages - 분석된 이미지
     * @param {Object} sections - 섹션 데이터
     * @returns {Object} 사용자 친화적 매핑 정보
     */
    formatMappingForDisplay(mappingResult, analyzedImages, sections) {
        const displayMapping = {
            successful: [],
            suggestions: [],
            unmapped: {
                images: [],
                sections: []
            },
            summary: {
                totalImages: analyzedImages.length,
                totalSections: Object.keys(sections).length,
                mappedImages: Object.keys(mappingResult.mappings).length,
                qualityScore: mappingResult.qualityScore,
                qualityLevel: mappingResult.qualityLevel
            }
        };

        // 성공적인 매핑
        Object.entries(mappingResult.mappings).forEach(([imageIndex, sectionName]) => {
            const idx = parseInt(imageIndex);
            displayMapping.successful.push({
                imageIndex: idx,
                imageName: analyzedImages[idx]?.name || `이미지 ${idx + 1}`,
                imageCaption: analyzedImages[idx]?.caption || '',
                sectionName: sectionName,
                sectionTitle: sections[sectionName]?.title || sectionName,
                confidence: mappingResult.confidence[idx] || 0
            });
        });

        // 제안사항
        Object.entries(mappingResult.suggestions).forEach(([imageIndex, suggestions]) => {
            const idx = parseInt(imageIndex);
            if (suggestions.length > 0) {
                displayMapping.suggestions.push({
                    imageIndex: idx,
                    imageName: analyzedImages[idx]?.name || `이미지 ${idx + 1}`,
                    imageCaption: analyzedImages[idx]?.caption || '',
                    suggestions: suggestions.map(s => ({
                        sectionName: s.section,
                        sectionTitle: sections[s.section]?.title || s.section,
                        confidence: s.confidence,
                        matchedKeywords: s.matchedKeywords
                    }))
                });
            }
        });

        // 매핑되지 않은 항목들
        displayMapping.unmapped.images = mappingResult.unmappedImages.map(item => ({
            index: item.index,
            name: item.image.name || `이미지 ${item.index + 1}`,
            caption: item.image.caption || ''
        }));

        displayMapping.unmapped.sections = mappingResult.unmappedSections.map(sectionName => ({
            name: sectionName,
            title: sections[sectionName]?.title || sectionName
        }));

        return displayMapping;
    },

    /**
     * 매핑 결과 내보내기
     * @param {Object} mappingResult - 매핑 결과
     * @returns {string} JSON 형태의 매핑 데이터
     */
    exportMapping(mappingResult) {
        const exportData = {
            mappings: mappingResult.mappings,
            confidence: mappingResult.confidence,
            qualityScore: mappingResult.qualityScore,
            timestamp: new Date().toISOString()
        };
        
        return JSON.stringify(exportData, null, 2);
    },

    /**
     * 매핑 데이터 가져오기
     * @param {string} jsonData - JSON 형태의 매핑 데이터
     * @returns {Object} 매핑 결과
     */
    importMapping(jsonData) {
        try {
            const importedData = JSON.parse(jsonData);
            
            if (!importedData.mappings || typeof importedData.mappings !== 'object') {
                throw new Error('유효하지 않은 매핑 데이터입니다.');
            }
            
            return {
                mappings: importedData.mappings,
                confidence: importedData.confidence || {},
                qualityScore: importedData.qualityScore || 0
            };
        } catch (error) {
            console.error('매핑 데이터 가져오기 실패:', error);
            throw new Error('매핑 데이터를 파싱할 수 없습니다.');
        }
    }
};

// 전역 스코프에 추가
window.ImageMapperService = ImageMapperService;