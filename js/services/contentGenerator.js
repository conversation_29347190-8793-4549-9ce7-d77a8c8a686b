// 콘텐츠 생성 서비스 모듈
const ContentGeneratorService = {
    /**
     * 블로그 섹션 콘텐츠 생성
     * @param {Object} step3Data - 3단계 데이터
     * @param {Array} analyzedImages - 분석된 이미지 목록
     * @param {string} editedImageUrl - 편집된 이미지 URL
     * @param {Object} options - 생성 옵션
     * @param {string} options.model - 사용할 모델
     * @param {string} options.customPrompt - 커스텀 프롬프트
     * @param {Function} options.onProgress - 진행상황 콜백
     * @returns {Promise<Object>} 생성된 섹션 데이터
     */
    async generateSections(step3Data, analyzedImages, editedImageUrl, options = {}) {
        console.log('📝 콘텐츠 생성 서비스 시작');
        
        try {
            // 진행상황 콜백 호출
            if (options.onProgress) {
                options.onProgress('콘텐츠 생성 준비 중...');
            }

            // 입력 데이터 유효성 검증
            this.validateInputData(step3Data, analyzedImages);

            // 프롬프트 생성
            const prompt = this.generatePrompt(step3Data, analyzedImages, options.customPrompt);
            const systemMessage = PromptConfig.getTextGenerationSystemMessage();

            if (options.onProgress) {
                options.onProgress('OpenAI API 호출 중...');
            }

            // 텍스트 생성 API 호출
            const response = await TextGenerationAPI.generateJsonText(
                prompt,
                systemMessage,
                {
                    model: options.model || APIConfig.defaults.textModel,
                    temperature: 1,
                    max_tokens: 4000
                }
            );

            if (options.onProgress) {
                options.onProgress('생성된 콘텐츠 처리 중...');
            }

            // JSON 응답 파싱
            const parsedContent = await TextGenerationAPI.parseJsonResponse(response);
            
            if (!parsedContent) {
                throw new Error('생성된 콘텐츠를 파싱할 수 없습니다.');
            }

            // 섹션 유효성 검증
            const validatedSections = this.validateAndProcessSections(parsedContent, editedImageUrl);

            if (options.onProgress) {
                options.onProgress('콘텐츠 생성 완료!');
            }

            console.log('✅ 콘텐츠 생성 서비스 완료');
            return validatedSections;

        } catch (error) {
            console.error('❌ 콘텐츠 생성 서비스 오류:', error);
            
            if (options.onProgress) {
                options.onProgress(`생성 실패: ${error.message}`);
            }
            
            // 폴백 섹션 반환
            console.log('폴백 섹션 사용');
            return this.getFallbackSections(step3Data, editedImageUrl);
        }
    },

    /**
     * 입력 데이터 유효성 검증
     * @param {Object} step3Data - 3단계 데이터
     * @param {Array} analyzedImages - 분석된 이미지
     * @throws {Error} 유효하지 않은 데이터인 경우
     */
    validateInputData(step3Data, analyzedImages) {
        if (!step3Data) {
            throw new Error('3단계 데이터가 없습니다.');
        }

        if (!step3Data.productName) {
            throw new Error('제품명이 없습니다.');
        }

        if (!analyzedImages || !Array.isArray(analyzedImages) || analyzedImages.length === 0) {
            throw new Error('분석된 이미지가 없습니다.');
        }

        console.log('✅ 입력 데이터 유효성 검증 통과');
    },

    /**
     * 프롬프트 생성
     * @param {Object} step3Data - 3단계 데이터
     * @param {Array} analyzedImages - 분석된 이미지
     * @param {string} customPrompt - 커스텀 프롬프트
     * @returns {string} 생성된 프롬프트
     */
    generatePrompt(step3Data, analyzedImages, customPrompt = null) {
        if (customPrompt && customPrompt.trim()) {
            console.log('커스텀 프롬프트 사용');
            return customPrompt.trim();
        }
        
        const prompt = PromptConfig.getTextGenerationPrompt(step3Data, analyzedImages);
        console.log('기본 프롬프트 사용');
        return prompt;
    },

    /**
     * 섹션 유효성 검증 및 처리
     * @param {Object} parsedContent - 파싱된 콘텐츠
     * @param {string} editedImageUrl - 편집된 이미지 URL
     * @returns {Object} 검증된 섹션 데이터
     */
    validateAndProcessSections(parsedContent, editedImageUrl) {
        const requiredSections = ['hero', 'warning', 'sevenStep', 'warranty', 'review', 'cta'];
        const validatedSections = {};

        // 필수 섹션 검증
        for (const section of requiredSections) {
            if (!parsedContent[section]) {
                throw new Error(`필수 섹션이 누락되었습니다: ${section}`);
            }
            
            const sectionData = parsedContent[section];
            
            // 섹션 데이터 유효성 검증
            if (!sectionData.title || !sectionData.content) {
                throw new Error(`섹션 ${section}에 title 또는 content가 없습니다.`);
            }
            
            // 최소 길이 검증
            if (sectionData.content.length < 50) {
                throw new Error(`섹션 ${section}의 내용이 너무 짧습니다.`);
            }
            
            validatedSections[section] = sectionData;
        }

        // CTA 섹션에 편집된 이미지 추가
        if (editedImageUrl && validatedSections.cta) {
            validatedSections.cta.image = editedImageUrl;
        }

        console.log('✅ 섹션 유효성 검증 완료');
        return validatedSections;
    },

    /**
     * 폴백 섹션 가져오기
     * @param {Object} step3Data - 3단계 데이터
     * @param {string} editedImageUrl - 편집된 이미지 URL
     * @returns {Object} 폴백 섹션 데이터
     */
    getFallbackSections(step3Data, editedImageUrl) {
        console.log('폴백 섹션 생성 중...');
        
        const fallbackSections = PromptConfig.getFallbackSections(step3Data, editedImageUrl);
        
        console.log('✅ 폴백 섹션 생성 완료');
        return fallbackSections;
    },

    /**
     * 지원되는 텍스트 모델 목록 가져오기
     * @returns {Array} 모델 목록
     */
    getSupportedModels() {
        return Object.keys(APIConfig.textModels).map(modelName => ({
            name: modelName,
            displayName: this.getModelDisplayName(modelName),
            config: APIConfig.getTextModelConfig(modelName)
        }));
    },

    /**
     * 모델 표시명 가져오기
     * @param {string} modelName - 모델명
     * @returns {string} 표시명
     */
    getModelDisplayName(modelName) {
        const displayNames = {
            'gpt-4o-mini': 'GPT-4o Mini (빠르고 경제적)',
            'gpt-4': 'GPT-4 (고품질, 느림)',
            'o3': 'o3 (신규 기본 모델)'
        };
        return displayNames[modelName] || modelName;
    },

    /**
     * 콘텐츠 생성 미리보기
     * @param {Object} step3Data - 3단계 데이터
     * @param {Array} analyzedImages - 분석된 이미지
     * @param {string} customPrompt - 커스텀 프롬프트
     * @returns {Object} 미리보기 정보
     */
    previewGeneration(step3Data, analyzedImages, customPrompt = null) {
        const prompt = this.generatePrompt(step3Data, analyzedImages, customPrompt);
        const systemMessage = PromptConfig.getTextGenerationSystemMessage();
        const modelConfig = APIConfig.getTextModelConfig(APIConfig.defaults.textModel);
        
        return {
            prompt: prompt,
            systemMessage: systemMessage,
            model: modelConfig.name,

            estimatedTime: this.estimateGenerationTime(modelConfig.name),
            estimatedCost: this.estimateGenerationCost(modelConfig.name, prompt.length)
        };
    },

    /**
     * 생성 시간 추정
     * @param {string} modelName - 모델명
     * @returns {string} 추정 시간
     */
    estimateGenerationTime(modelName) {
        const times = {
            'gpt-4o-mini': '10-20초',
            'gpt-4': '30-60초'
        };
        return times[modelName] || '20-40초';
    },

    /**
     * 생성 비용 추정
     * @param {string} modelName - 모델명
     * @param {number} promptLength - 프롬프트 길이
     * @returns {string} 추정 비용
     */
    estimateGenerationCost(modelName, promptLength) {
        // 대략적인 토큰 수 계산 (1토큰 ≈ 4글자)
        const estimatedTokens = Math.ceil(promptLength / 4) + 4000; // 응답 토큰 포함
        
        const costs = {
            'gpt-4o-mini': estimatedTokens * 0.00015 / 1000, // $0.15 per 1K tokens
            'gpt-4': estimatedTokens * 0.03 / 1000 // $30 per 1K tokens
        };
        
        const cost = costs[modelName] || 0;
        return `약 $${cost.toFixed(4)}`;
    },

    /**
     * 섹션별 키워드 추출
     * @param {Object} sections - 생성된 섹션
     * @returns {Object} 섹션별 키워드
     */
    extractKeywords(sections) {
        const keywords = {};
        
        for (const [sectionName, sectionData] of Object.entries(sections)) {
            if (sectionData && sectionData.content) {
                // 간단한 키워드 추출 (향후 개선 가능)
                const content = sectionData.content.toLowerCase();
                const words = content.match(/\b\w{3,}\b/g) || [];
                const wordCount = {};
                
                words.forEach(word => {
                    wordCount[word] = (wordCount[word] || 0) + 1;
                });
                
                // 빈도순으로 정렬하여 상위 5개 키워드 추출
                keywords[sectionName] = Object.entries(wordCount)
                    .sort(([,a], [,b]) => b - a)
                    .slice(0, 5)
                    .map(([word]) => word);
            }
        }
        
        return keywords;
    },

    /**
     * 생성 히스토리 관리 (향후 구현용)
     * @param {Object} generationInfo - 생성 정보
     */
    saveGenerationHistory(generationInfo) {
        // 향후 생성 히스토리 저장 기능 구현
        console.log('생성 히스토리 저장 (향후 구현):', generationInfo);
    }
};

// 전역 스코프에 추가
window.ContentGeneratorService = ContentGeneratorService;