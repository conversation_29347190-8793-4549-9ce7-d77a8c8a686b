// API 설정 관리 모듈
const APIConfig = {
    // API 키 (실제 환경에서는 환경변수나 보안 저장소에서 가져와야 함)
    apiKey: "********************************************************************************************************************************************************************",
    
    // OpenAI API 엔드포인트
    endpoints: {
        imageEdit: 'https://api.openai.com/v1/images/edits',
        chatCompletion: 'https://api.openai.com/v1/chat/completions'
    },
    
    // 이미지 편집 모델 설정
    imageModels: {
        'dall-e-2': {
            name: 'dall-e-2',
            supportsResponseFormat: true,
            defaultSize: '1024x1024',
            maxSize: '1024x1024'
        },
        'gpt-image-1': {
            name: 'gpt-image-1',
            supportsResponseFormat: false,
            defaultSize: '1024x1024',
            maxSize: '1024x1024'
        }
    },
    
    // 텍스트 생성 모델 설정
    textModels: {
        'o3': {
            name: 'o3',
            maxTokens: 2000,
            temperature: 1
        },
        'gpt-4o-mini': {
            name: 'gpt-4o-mini',
            maxTokens: 2000,
            temperature: 0.7
        },
        'gpt-4': {
            name: 'gpt-4',
            maxTokens: 2000,
            temperature: 0.7
        }
    },
    
    // 기본 설정
    defaults: {
        imageModel: 'dall-e-2',
        textModel: 'o3',
        imageSize: '1024x1024',
        imageCount: 1
    },
    
    // API 키 검증
    validateApiKey() {
        if (!this.apiKey || this.apiKey.length < 10) {
            throw new Error('유효하지 않은 API 키입니다.');
        }
        return true;
    },
    
    // API 키 가져오기
    getApiKey() {
        this.validateApiKey();
        return this.apiKey;
    },
    
    // 모델 설정 가져오기
    getImageModelConfig(modelName) {
        const model = this.imageModels[modelName] || this.imageModels[this.defaults.imageModel];
        return { ...model };
    },
    
    getTextModelConfig(modelName) {
        const model = this.textModels[modelName] || this.textModels[this.defaults.textModel];
        return { ...model };
    },
    
    // 공통 헤더 생성
    getAuthHeaders() {
        this.validateApiKey();
        return {
            'Authorization': `Bearer ${this.apiKey}`
        };
    },
    
    getJsonHeaders() {
        return {
            ...this.getAuthHeaders(),
            'Content-Type': 'application/json'
        };
    }
};

// 전역 스코프에 추가
window.APIConfig = APIConfig;