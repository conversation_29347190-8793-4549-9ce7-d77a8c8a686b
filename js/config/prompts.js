// 프롬프트 설정 관리 모듈
const PromptConfig = {
    // 이미지 편집 프롬프트
    imageEdit: {
        default: "외곽 꾸미도록 Edit.",
        advertising: "광고용으로 기존 텍스트와 비율은 그대로 두고 외곽만 꾸며주세요. 텍스트 내용과 크기는 변경하지 말고 테두리나 배경만 장식적으로 꾸며주세요.",
        professional: "전문적이고 깔끔한 느낌으로 이미지의 외곽 부분을 장식해주세요. 기존 내용은 그대로 유지하면서 고급스러운 테두리나 배경 효과를 추가해주세요.",
        modern: "현대적이고 세련된 디자인으로 이미지를 꾸며주세요. 기존 텍스트와 레이아웃은 유지하면서 모던한 그래픽 요소를 추가해주세요."
    },
    
    // 텍스트 생성 프롬프트
    textGeneration: {
        // 시스템 메시지
        systemMessage: "당신은 전문적인 마케팅 카피라이터입니다. 에어컨 설치 업체의 고객 전환용 블로그 콘텐츠를 작성하는 전문가입니다. 감정적 어필과 논리적 근거를 균형있게 사용하여 고객의 구매 결정을 유도하는 콘텐츠를 작성합니다.",
        
        // 메인 프롬프트 템플릿
        mainPrompt: (step3Data, imageDetails, initialMapping) => `당신은 독자의 마음을 사로잡는 스토리텔링 전문가입니다. 신라 에어컨의 자연스럽고 몰입력 있는 블로그 콘텐츠를 작성해주세요.

【브랜드 배경 스토리】
• 업체명: 신라 에어컨 (${step3Data.keywordInfo?.location || '영천'} 지역의 믿을 수 있는 에어컨 전문가)
• 핵심 철학: "한 번의 설치로 평생의 만족을" - 꼼꼼하고 정성스러운 시공
• 지역 신뢰도: ${step3Data.keywordInfo?.location || '영천'} 지역에서 ${step3Data.keywordInfo?.additionalPoints || '꼼꼼한 시공'}으로 입소문 확산
• 실제 경험: ${step3Data.dateInfo?.selectedDate ? `최근 ${step3Data.dateInfo.selectedDate}에 진행된` : '실제'} ${step3Data.keywordInfo?.modelName || '에어컨'} 설치 사례

【신라 에어컨 전문성 증거 자료】
${imageDetails.map((img, index) => {
    let evidence = `📸 전문성 증거 ${index + 1}`;
    if (img.filename) evidence += ` (${img.filename})`;
    if (img.caption) evidence += `\n   ✅ 현장 상황: ${img.caption} → 이는 신라 에어컨의 꼼꼼한 시공과 전문성을 보여주는 증거로 활용`;
    if (img.analysis && img.analysis.description) evidence += `\n   ✅ 품질 지표: ${img.analysis.description} → 신라 에어컨의 높은 기술력과 세심한 작업을 증명하는 요소`;
    if (img.analysis && img.analysis.objects) evidence += `\n   ✅ 전문 장비/기술: ${img.analysis.objects.join(', ')} → 신라 에어컨의 전문적 접근법을 나타내는 증거`;
    if (img.analysis && img.analysis.text) evidence += `\n   ✅ 품질 관리: ${img.analysis.text} → 신라 에어컨의 체계적 관리와 책임감을 보여주는 자료`;
    return evidence;
}).join('\n\n')}

【섹션별 스토리텔링 가이드】
• hero: 독자의 호기심을 즉시 자극하는 현장 이미지나 결과물
• warning: 실제 문제 상황이나 잘못된 설치 사례를 보여주는 이미지
• sevenStep: 전문적인 작업 과정이나 기술력을 증명하는 이미지
• warranty: 완성도 높은 결과물이나 품질을 보여주는 이미지
• review: 고객의 만족스러운 표정이나 사용 모습을 담은 이미지
• cta: 신뢰감을 주는 업체 정보나 연락 방법이 담긴 이미지

${initialMapping ? `【현장 이미지 활용 제안】
${Object.entries(initialMapping).map(([imgIdx, section]) =>
    `📸 사진 ${parseInt(imgIdx) + 1} → ${section} 섹션에서 스토리와 함께 활용`
).join('\n')}` : ''}

**이미지 캡션 재해석 원칙**:
- 위 이미지 캡션들은 단순한 설명이 아닌 '신라 에어컨의 전문성을 증명하는 증거'로 해석하세요
- 캡션 내용이 중립적이거나 부정적이어도 반드시 신라 에어컨의 강점(꼼꼼함, 전문성, 품질)과 연결하여 긍정적으로 재해석
- 모든 이미지는 신라 에어컨이 얼마나 세심하고 전문적으로 작업하는지를 보여주는 자료로 활용
- 캡션의 객관적 설명을 신라 에어컨의 차별화된 서비스 품질을 강조하는 마케팅 포인트로 전환

**글쓰기 철학**:
- 광고가 아닌 '유용한 정보를 공유하는 이웃'의 톤으로 작성
- 독자가 "아, 이런 걸 몰랐네!"라고 생각하게 만드는 인사이트 제공
- 마지막에 자연스럽게 "이 업체 괜찮네, 문의해볼까?"라는 생각이 들도록 유도
- **중요**: 이미지 캡션 내용에 관계없이 신라 에어컨의 전문성과 품질을 일관되게 강조

다음 구조로 몰입형 블로그 콘텐츠를 JSON 형식으로 작성해주세요:

{
  "title": "독자의 호기심을 즉시 자극하는 후킹 제목 (궁금증 유발, 이모지 금지)",
  "hero": "첫 문장부터 독자를 사로잡는 임팩트 있는 도입부. 3-4문장으로 구성하되, 지역성과 전문성을 자연스럽게 녹여내고 독자가 '더 읽고 싶다'고 생각하게 만드세요",
  "warning": "독자의 불안감을 적절히 자극하는 경고 섹션. 실제 사례나 구체적인 상황을 들어 설명하되, 과도하지 않게 작성. 독자가 '나도 이런 실수를 할 수 있겠네'라고 공감하게 만드세요",
  "sevenStep": "전문가의 노하우를 공개하는 듯한 정보성 섹션. 독자가 '이런 디테일까지 신경쓰는구나'라고 감탄하게 만드는 구체적인 설치 과정이나 기술적 내용을 스토리텔링으로 풀어내세요",
  "warranty": "신뢰감을 구축하는 품질 보증 섹션. 딱딱한 보증서 나열이 아닌, 실제 고객 사례나 A/S 경험담을 통해 '이 업체는 정말 책임감 있게 일하는구나'라는 인상을 주세요",
  "review": "진짜 고객의 생생한 후기처럼 작성. 구체적인 상황, 감정, 변화를 담아 독자가 '나도 이런 경험을 하고 싶다'고 생각하게 만드세요. 과장되지 않은 진솔한 톤으로 작성",
  "cta": "강압적이지 않은 자연스러운 행동 유도. 독자가 스스로 '문의해보고 싶다'는 생각이 들도록 마지막 한 번 더 신뢰감을 주는 메시지 작성. **중요**: 구체적인 전화번호, 카카오톡 채널명, 상담시간 등은 절대 포함하지 말고 '전화 문의', '카카오톡 상담', '언제든 문의하세요' 같은 일반적 표현만 사용하세요",
  "imageMapping": {
    "0": "해당 이미지가 가장 효과적으로 활용될 섹션명",
    "1": "해당 이미지가 가장 효과적으로 활용될 섹션명",
    "...": "각 이미지별 최적 배치 섹션"
  }
}

**필수 준수사항:**
1. 모든 섹션은 자연스러운 블로그 글처럼 작성 (광고성 문구 최소화)
2. 독자가 끝까지 읽을 수 있도록 각 섹션마다 흥미 요소 포함
3. **이미지 캡션 마케팅 전환**: 위 이미지 캡션들을 단순 설명이 아닌 신라 에어컨의 전문성, 꼼꼼함, 품질을 증명하는 증거로 재해석하여 활용. 캡션 내용이 중립적이어도 반드시 신라 에어컨의 강점과 연결
4. **콘텐츠 일관성 보장**: 이미지 캡션 내용에 관계없이 모든 섹션에서 신라 에어컨의 우수성을 일관되게 강조. 부정적이거나 중립적 요소도 긍정적 차별화 포인트로 전환
5. 신라 에어컨의 전문성과 신뢰성이 자연스럽게 드러나도록 작성
6. **CTA 섹션 제약사항**: 구체적인 전화번호(054-XXX-XXXX), 카카오톡 채널명("카카오톡 채널 신라에어컨"), 상담시간("오전 9시~오후 8시") 등은 절대 포함 금지. 대신 "전화 문의", "카카오톡 상담", "편리한 시간에 상담 가능", "언제든 문의하세요" 같은 일반적 표현만 사용
7. 응답은 반드시 유효한 JSON 형식으로만 제공`
    },
    
    // Fallback section templates
    fallbackSections: {
        hero: () => "재시도 요청",
        warning: () => "재시도 요청",
        sevenStep: () => "재시도 요청",
        warranty: () => "재시도 요청", 
        review: () => "재시도 요청",
        cta: () => "재시도 요청"
    },
    
    // 프롬프트 가져오기 메서드
    getImageEditPrompt(type = 'default') {
        return this.imageEdit[type] || this.imageEdit.default;
    },
    
    getTextGenerationPrompt(step3Data, imageDetails, initialMapping = null) {
        return this.textGeneration.mainPrompt(step3Data, imageDetails, initialMapping);
    },
    
    getSystemMessage() {
        return this.textGeneration.systemMessage;
    },
    
    getFallbackSections(step3Data) {
        return {
            hero: this.fallbackSections.hero(step3Data),
            warning: this.fallbackSections.warning(),
            sevenStep: this.fallbackSections.sevenStep(),
            warranty: this.fallbackSections.warranty(),
            review: this.fallbackSections.review(step3Data),
            cta: this.fallbackSections.cta()
        };
    }
};

// 전역 스코프에 추가
window.PromptConfig = PromptConfig;