// 프롬프트 설정 관리 모듈
const PromptConfig = {
    // 이미지 편집 프롬프트
    imageEdit: {
        default: "외곽 꾸미도록 Edit.",
        advertising: "광고용으로 기존 텍스트와 비율은 그대로 두고 외곽만 꾸며주세요. 텍스트 내용과 크기는 변경하지 말고 테두리나 배경만 장식적으로 꾸며주세요.",
        professional: "전문적이고 깔끔한 느낌으로 이미지의 외곽 부분을 장식해주세요. 기존 내용은 그대로 유지하면서 고급스러운 테두리나 배경 효과를 추가해주세요.",
        modern: "현대적이고 세련된 디자인으로 이미지를 꾸며주세요. 기존 텍스트와 레이아웃은 유지하면서 모던한 그래픽 요소를 추가해주세요."
    },
    
    // 텍스트 생성 프롬프트
    textGeneration: {
        // 시스템 메시지
        systemMessage: "당신은 전문적인 마케팅 카피라이터입니다. 에어컨 설치 업체의 고객 전환용 블로그 콘텐츠를 작성하는 전문가입니다. 감정적 어필과 논리적 근거를 균형있게 사용하여 고객의 구매 결정을 유도하는 콘텐츠를 작성합니다.",
        
        // 메인 프롬프트 템플릿
        mainPrompt: (step3Data, analyzedImages) => `신라 에어컨 브랜드의 효과적인 고객 전환용 블로그 아티클을 생성해주세요.

【브랜드 정보】
• 브랜드명: 신라 에어컨 (전문 에어컨 설치 업체)
• 핵심 차별점: 꼼꼼하게 설치 및 시공
• 지역 전문성: ${step3Data.keywordInfo?.location || '영천'} 지역 맞춤 서비스

【가터 정보】
• 설치일: ${step3Data.dateInfo?.selectedDate || ''}
• 설치 모델: ${step3Data.keywordInfo?.modelName || ''}
• 고객 인식: ${step3Data.keywordInfo?.additionalPoints || '꼼꼼하게 해준다는 인식'}

【이미지 정보】
${analyzedImages.map((img, index) => `• 이미지 ${index + 1}: ${img.caption}`).join('\n')}

다음 7개 섹션으로 구성된 고객 전환 최적화 블로그를 JSON 형식으로 작성해주세요:

{
  "title": "고객의 클릭을 유도하는 후킹 제목 (이모지 사용 금지)",
  "hero": "Hero 헤드라인 - 3줄로 작성. 임팩트 있게, 지역명과 전문성을 강조, 핵심 혜택 포함",
  "warning": "넛지 위험 경고(공포심 자극) 섹션 - 잘못된 에어컨 설치로 인한 문제점들을 구체적으로 나열. 공포심을 자극하되 과하지 않게. 블로그 글 형태로 길게 작성",
  "sevenStep": "설명 섹션 - 고객 전환용 설명 섹션1. 블로그 글 형태로 길게 작성",
  "warranty": "설명 섹션 - 고객 전환용 설명 섹션2. 블로그 글 형태로 길게 작성",
  "review": "설명 섹션 - 실제 고객 후기 형식으로 신뢰도 있게 작성 (지역명 포함). 블로그 글 형태로 길게 작성",
  "cta": "넛지 CTA 섹션 - 위 내용을 포함하여 마지막 강력한 행동 유도 문구. 블로그 글 형태로 길게 작성"
}

각 섹션은 블로그 글 형태로 충분히 길게 작성하되, 감정적 공감과 논리적 근거를 균형있게 제시하여 고객의 구매 결정을 자연스럽게 유도해주세요. 제목은 이모지 없이 후킹하도록 작성하세요. 응답은 반드시 유효한 JSON 형식으로만 제공해주세요.`
    },
    
    // 폴백 섹션 템플릿
    fallbackSections: {
        hero: (step3Data) => `🌟 ${step3Data.keywordInfo?.location || '영천'} 지역 1위 신라 에어컨 | ${step3Data.keywordInfo?.modelName || 'LG 에어컨'} 전문 설치\n✅ 15% 전력비 절감 보장 ✅ 3년 무상 A/S ✅ 당일 설치 가능\n${step3Data.keywordInfo?.additionalPoints || '꼼꼼한 시공'}으로 검증받은 신라 에어컨과 함께 시원한 여름을 준비하세요!`,
        
        warning: () => `⚠️ 에어컨 설치, 잘못하면 이런 일이 생깁니다\n\n❌ 부실 업체 선택 시 발생하는 문제들:\n• 냉매 누출로 인한 냉방 효율 50% 감소\n• 결로수 역류로 벽지 손상 및 곰팡이 번식\n• 전기 배선 불량으로 화재 위험 증가\n• 소음 문제로 이웃 갈등 발생\n\n💡 신라 에어컨은 20년 경력의 전문 기술진이 이 모든 문제를 사전 차단합니다`,
        
        sevenStep: () => `✅ 신라 에어컨만의 꼼꼼한 7단계 설치 프로세스\n\n1️⃣ 실내외 환경 정밀 진단  \n2️⃣ 최적 모델 및 위치 선정  \n3️⃣ 전문 장비 활용 정밀 수평 · 수직 시공  \n4️⃣ 배관·배선 밀착 단열 및 정리  \n5️⃣ 진공·가스 누설 테스트 2중 진행  \n6️⃣ 시험 가동 후 세밀 성능 체크  \n7️⃣ 현장 정리 및 사용·관리 방법 친절 안내\n\n각 단계마다 체크리스트를 작성해 고객에게 제공하므로 안심하고 맡기실 수 있습니다.`,
        
        warranty: () => `🛡️ 신라 에어컨 프리미엄 보증 서비스\n\n🎯 업계 최고 수준 보장\n• 설치 후 3년 이내 무상 A/S 및 부품 교체  \n• 냉매 누설·소음·결로 등 문제 발생 시 24시간 내 출장 대응  \n• 연 1회 무료 점검 & 필터 세척 서비스  \n• 고객 과실 제외, 추가 비용 없이 완전 보장\n\n신라 에어컨 서비스를 선택하면 설치 이후까지 안심할 수 있습니다.`,
        
        review: (step3Data) => `⭐ 신라 에어컨 고객 실제 후기 (만족도 98.7%)\n\n💬 "${step3Data.keywordInfo?.location || '영천'}에서 신라 에어컨 설치 후 2년째 사용 중입니다. 전기료가 월 3만원 절약되고, 고장 한 번 없이 완벽해요. ${step3Data.keywordInfo?.additionalPoints || '꼼꼼한 시공'} 정말 인정합니다!"\n- ${step3Data.keywordInfo?.location || '영천'} 거주 김○○님 (아파트 거주)\n\n💬 "다른 업체 견적보다 조금 비쌌지만 3년 보증 때문에 선택했는데 정말 잘한 것 같아요. 여름마다 점검 와주시고 진짜 프로페셔널합니다."\n- 이○○님 (단독주택)\n\n📊 고객 만족도: ⭐⭐⭐⭐⭐ (5.0/5.0)`,
        
        cta: () => `📞 지금 바로 신라 에어컨에 상담 신청하세요!\n\n🎯 **무료 상담 및 견적**\n📱 전화: 1588-0000\n💬 카카오톡: @신라에어컨\n🌐 온라인 상담: www.shilla-aircon.co.kr\n\n⏰ **운영시간**\n평일: 오전 8시 ~ 오후 10시\n주말: 오전 9시 ~ 오후 8시\n긴급출동: 24시간 가능\n\n💰 **특별 혜택**\n✅ 첫 상담 고객 10% 할인\n✅ 당일 계약시 추가 5% 할인\n✅ 무료 점검 서비스 제공\n\n지금 전화하시면 전문 상담사가 친절하게 안내해드립니다!`
    },
    
    // 프롬프트 가져오기 메서드
    getImageEditPrompt(type = 'default') {
        return this.imageEdit[type] || this.imageEdit.default;
    },
    
    getTextGenerationPrompt(step3Data, analyzedImages) {
        return this.textGeneration.mainPrompt(step3Data, analyzedImages);
    },
    
    getSystemMessage() {
        return this.textGeneration.systemMessage;
    },
    
    getFallbackSections(step3Data) {
        return {
            hero: this.fallbackSections.hero(step3Data),
            warning: this.fallbackSections.warning(),
            sevenStep: this.fallbackSections.sevenStep(),
            warranty: this.fallbackSections.warranty(),
            review: this.fallbackSections.review(step3Data),
            cta: this.fallbackSections.cta()
        };
    }
};

// 전역 스코프에 추가
window.PromptConfig = PromptConfig;