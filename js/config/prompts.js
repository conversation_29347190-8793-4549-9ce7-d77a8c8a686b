// 프롬프트 설정 관리 모듈
const PromptConfig = {
    // 이미지 편집 프롬프트
    imageEdit: {
        default: "외곽 꾸미도록 Edit.",
        advertising: "광고용으로 기존 텍스트와 비율은 그대로 두고 외곽만 꾸며주세요. 텍스트 내용과 크기는 변경하지 말고 테두리나 배경만 장식적으로 꾸며주세요.",
        professional: "전문적이고 깔끔한 느낌으로 이미지의 외곽 부분을 장식해주세요. 기존 내용은 그대로 유지하면서 고급스러운 테두리나 배경 효과를 추가해주세요.",
        modern: "현대적이고 세련된 디자인으로 이미지를 꾸며주세요. 기존 텍스트와 레이아웃은 유지하면서 모던한 그래픽 요소를 추가해주세요."
    },
    
    // 텍스트 생성 프롬프트
    textGeneration: {
        // 시스템 메시지
        systemMessage: "당신은 전문적인 마케팅 카피라이터입니다. 에어컨 설치 업체의 고객 전환용 블로그 콘텐츠를 작성하는 전문가입니다. 감정적 어필과 논리적 근거를 균형있게 사용하여 고객의 구매 결정을 유도하는 콘텐츠를 작성합니다.",
        
        // 메인 프롬프트 템플릿
        mainPrompt: (step3Data, imageDetails, initialMapping) => `신라 에어컨 브랜드의 효과적인 고객 전환용 블로그 아티클을 생성해주세요.

【브랜드 정보】
• 브랜드명: 신라 에어컨 (전문 에어컨 설치 업체)
• 핵심 차별점: 꼼꼼하게 설치 및 시공
• 지역 전문성: ${step3Data.keywordInfo?.location || '영천'} 지역 맞춤 서비스

【작업 정보】
• 설치일: ${step3Data.dateInfo?.selectedDate || '날짜 미상'}
• 설치 모델: ${step3Data.keywordInfo?.modelName || ''}
• 고객 인식: ${step3Data.keywordInfo?.additionalPoints || '꼼꼼하게 해준다는 인식'}

【상세 이미지 정보】
${imageDetails.map((img, index) => {
    let details = `• 이미지 ${index + 1}:`;
    if (img.filename) details += `\n  - 파일명: ${img.filename}`;
    if (img.caption) details += `\n  - 캡션: ${img.caption}`;
    if (img.analysis && img.analysis.description) details += `\n  - 분석 결과: ${img.analysis.description}`;
    if (img.analysis && img.analysis.objects) details += `\n  - 감지된 객체: ${img.analysis.objects.join(', ')}`;
    if (img.analysis && img.analysis.text) details += `\n  - 텍스트 내용: ${img.analysis.text}`;
    return details;
}).join('\n\n')}

【섹션별 이미지 배치 가이드라인】
• hero: 브랜드 로고, 대표 제품, 메인 비주얼에 적합한 이미지
• warning: 문제 상황, 위험 요소, 주의사항을 보여주는 이미지
• sevenStep: 설치 과정, 작업 단계, 기술적 내용을 설명하는 이미지
• warranty: 품질 보증, 인증서, 완성된 작업 결과를 보여주는 이미지
• review: 고객 만족, 사용 후기, 실제 설치 현장을 보여주는 이미지
• cta: 연락처 정보, 문의 방법, 행동 유도에 적합한 이미지

${initialMapping ? `【AI 추천 이미지 매핑】
${Object.entries(initialMapping).map(([imgIdx, section]) =>
    `• 이미지 ${parseInt(imgIdx) + 1} → ${section} 섹션 (추천)`
).join('\n')}` : ''}

다음 구조로 고객 전환 최적화 블로그를 JSON 형식으로 작성해주세요:

{
  "title": "고객의 클릭을 유도하는 후킹 제목 (이모지 사용 금지)",
  "hero": "Hero 헤드라인 - 3줄로 작성. 임팩트 있게, 지역명과 전문성을 강조, 핵심 혜택 포함",
  "warning": "넛지 위험 경고(공포심 자극) 섹션 - 잘못된 에어컨 설치로 인한 문제점들을 구체적으로 나열. 공포심을 자극하되 과하지 않게. 블로그 글 형태로 길게 작성",
  "sevenStep": "설명 섹션 - 고객 전환용 설명 섹션1. 블로그 글 형태로 설명형으로 작성. 위 이미지 정보를 자연스럽게 참조",
  "warranty": "설명 섹션 - 고객 전환용 설명 섹션2. 블로그 글 형태로 전환용으로 작성",
  "review": "설명 섹션 - 실제 고객 후기 형식으로 신뢰도 있게 작성. 블로그 글 형태로 길게 작성",
  "cta": "넛지 CTA 섹션 - 위 내용을 포함하여 마지막 강력한 행동 유도 문구",
  "imageMapping": {
    "0": "추천 섹션명",
    "1": "추천 섹션명",
    "...": "각 이미지 인덱스별로 가장 적합한 섹션명 지정"
  }
}

**중요 지시사항:**
1. 각 섹션 내용은 위 이미지 정보를 자연스럽게 참조하여 작성하세요
2. imageMapping에서 각 이미지가 어느 섹션에 가장 적합한지 판단하여 매핑하세요
3. 이미지 캡션과 분석 결과를 바탕으로 섹션 내용과의 연관성을 고려하세요
4. 응답은 반드시 유효한 JSON 형식으로만 제공해주세요`
    },
    
    // Fallback section templates
    fallbackSections: {
        hero: () => "재시도 요청",
        warning: () => "재시도 요청",
        sevenStep: () => "재시도 요청",
        warranty: () => "재시도 요청", 
        review: () => "재시도 요청",
        cta: () => "재시도 요청"
    },
    
    // 프롬프트 가져오기 메서드
    getImageEditPrompt(type = 'default') {
        return this.imageEdit[type] || this.imageEdit.default;
    },
    
    getTextGenerationPrompt(step3Data, imageDetails, initialMapping = null) {
        return this.textGeneration.mainPrompt(step3Data, imageDetails, initialMapping);
    },
    
    getSystemMessage() {
        return this.textGeneration.systemMessage;
    },
    
    getFallbackSections(step3Data) {
        return {
            hero: this.fallbackSections.hero(step3Data),
            warning: this.fallbackSections.warning(),
            sevenStep: this.fallbackSections.sevenStep(),
            warranty: this.fallbackSections.warranty(),
            review: this.fallbackSections.review(step3Data),
            cta: this.fallbackSections.cta()
        };
    }
};

// 전역 스코프에 추가
window.PromptConfig = PromptConfig;