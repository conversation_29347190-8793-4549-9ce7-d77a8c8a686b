const express = require('express');
const bodyParser = require('body-parser');
const OpenAI = require('openai');
const fs = require('fs');
const path = require('path');
const fetch = require('node-fetch');

const app = express();
const port = 3000;

// Load API key from environment variable
const openai = new OpenAI({
    apiKey: process.env.OPENAI_API_KEY || "********************************************************************************************************************************************************************",
});

// Middleware to parse JSON bodies, with a higher limit for image data
app.use(bodyParser.json({ limit: '10mb' }));

// API endpoint for image editing
app.post('/api/openai-image-edit', async (req, res) => {
    const { prompt, image } = req.body;

    if (!prompt || !image) {
        return res.status(400).json({ error: { message: 'Prompt and image are required.' } });
    }

    try {
        // The image is a base64 string, so we need to decode it to a buffer
        const base64Data = image.replace(/^data:image\/png;base64,/, "");
        const imageBuffer = Buffer.from(base64Data, 'base64');

        // Create a temporary file for the API call
        const tempImagePath = path.join(__dirname, 'temp_image.png');
        fs.writeFileSync(tempImagePath, imageBuffer);

        // Call the OpenAI API
        const response = await openai.images.edit({
            model: 'dall-e-2',
            image: fs.createReadStream(tempImagePath),
            prompt: prompt,
            n: 1,
            size: '1024x1024'
        });

        // Clean up the temporary file
        fs.unlinkSync(tempImagePath);

        res.json(response);

    } catch (error) {
        console.error('Error calling OpenAI API:', error);
        res.status(500).json({ error: { message: 'Failed to edit image.', details: error.message } });
    }
});

// Serve static files after API routes
app.use(express.static(__dirname));

app.listen(port, () => {
    console.log(`Server listening at http://localhost:${port}`);
});