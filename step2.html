<!DOCTYPE html>
<html lang="ko">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>2단계: 키워드 입력 - 신라 에어컨 콘텐츠 작성기</title>
    <link rel="stylesheet" href="styles/main.css">
    <link rel="stylesheet" href="styles/components.css">
</head>
<body>
    <div id="app">
        <header class="header">
            <h1><span class="brand-red">신라</span> <span class="brand-blue">에어컨</span> 콘텐츠 작성기</h1>
            <div class="progress-container">
                <div class="progress-bar" id="progress-bar"></div>
                <span class="progress-text" id="progress-text">33%</span>
            </div>
        </header>
        
        <main class="main-container">
            <!-- 브레드크럼 네비게이션 -->
            <nav class="breadcrumb-nav">
                <div id="breadcrumb"></div>
            </nav>
            
            <!-- 단계 컨테이너 -->
            <div id="step-container" class="step-container">
                <!-- 키워드 입력 컴포넌트가 여기에 로드됩니다 -->
            </div>
            
            <!-- 네비게이션 -->
            <div class="navigation">
                <button id="prev-btn" class="nav-btn">이전</button>
                <div class="step-indicator">
                    <div class="step-progress">
                        <span id="current-step">2</span> / <span id="total-steps">4</span>
                    </div>
                    <div class="step-name" id="step-name">키워드 입력</div>
                    <div class="step-dots">
                        <div class="step-dot completed"></div>
                        <div class="step-dot active"></div>
                        <div class="step-dot"></div>
                        <div class="step-dot"></div>
                    </div>
                </div>
                <button id="next-btn" class="nav-btn">다음</button>
            </div>
        </main>
    </div>
    
    <!-- 스크립트 로딩 -->
    <script src="js/utils.js"></script>
    <script src="js/stateManager.js"></script>
    <script src="js/navigation.js"></script>
    <script src="js/components/keywordInput.js"></script>
    
    <script>
        // 페이지별 초기화
        document.addEventListener('DOMContentLoaded', () => {
            // 키워드 입력 컴포넌트 로드
            const container = document.getElementById('step-container');
            if (KeywordInput) {
                container.innerHTML = KeywordInput.render();
                Utils.fadeIn(container);
                
                // 컴포넌트 초기화
                KeywordInput.init();
                
                // 이전 데이터 복원
                const savedData = StateManager.loadKeywordData();
                if (savedData.modelName) {
                    const modelInput = document.getElementById('model-name');
                    if (modelInput) modelInput.value = savedData.modelName;
                }
                if (savedData.location) {
                    const locationInput = document.getElementById('location');
                    if (locationInput) locationInput.value = savedData.location;
                }
                if (savedData.additionalPoints) {
                    const pointsInput = document.getElementById('additional-points');
                    if (pointsInput) pointsInput.value = savedData.additionalPoints;
                }
                
                // 실시간 데이터 저장 및 네비게이션 업데이트
                const inputs = ['model-name', 'location', 'additional-points'];
                inputs.forEach(inputId => {
                    const input = document.getElementById(inputId);
                    if (input) {
                        input.addEventListener('input', () => {
                            StateManager.saveKeywordData(KeywordInput.getData());
                            Navigation.setupNavigationButtons();
                        });
                    }
                });
                
                // 초기 네비게이션 상태 업데이트
                setTimeout(() => {
                    Navigation.setupNavigationButtons();
                }, 100);
            }
            
            // 브레드크럼 생성
            Navigation.createBreadcrumb();
        });
    </script>
</body>
</html>