<!DOCTYPE html>
<html lang="ko">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>3단계: 이미지 업로드 - 신라 에어컨 콘텐츠 작성기</title>
    <link rel="stylesheet" href="styles/main.css">
    <link rel="stylesheet" href="styles/components.css">
</head>
<body>
    <div id="app">
        <header class="header">
            <h1><span class="brand-red">신라</span> <span class="brand-blue">에어컨</span> 콘텐츠 작성기</h1>
            <div class="progress-container">
                <div class="progress-bar" id="progress-bar"></div>
                <span class="progress-text" id="progress-text">50%</span>
            </div>
        </header>
        
        <main class="main-container">
            <!-- 브레드크럼 네비게이션 -->
            <nav class="breadcrumb-nav">
                <div id="breadcrumb"></div>
            </nav>
            
            <!-- 단계 컨테이너 -->
            <div id="step-container" class="step-container">
                <!-- 이미지 업로드 컴포넌트가 여기에 로드됩니다 -->
            </div>
            

            <!-- 네비게이션 -->
            <div class="navigation">
                <button id="prev-btn" class="nav-btn">이전</button>
                <div class="step-indicator">
                    <div class="step-progress">
                        <span id="current-step">3</span> / <span id="total-steps">4</span>
                    </div>
                    <div class="step-name" id="step-name">이미지 업로드</div>
                    <div class="step-dots">
                        <div class="step-dot completed"></div>
                        <div class="step-dot completed"></div>
                        <div class="step-dot active"></div>
                        <div class="step-dot"></div>
                    </div>
                </div>
                <button id="next-btn" class="nav-btn">다음</button>
            </div>
        </main>
    </div>
    
    <!-- 스크립트 로딩 -->
    <script src="js/utils.js"></script>
    <script src="js/stateManager.js"></script>
    <script src="js/navigation.js"></script>
    <script src="js/components/imageUpload.js"></script>
    
    <script>
        // 페이지별 초기화
        document.addEventListener('DOMContentLoaded', () => {
            // 이미지 업로드 컴포넌트 로드
            const container = document.getElementById('step-container');
            if (ImageUpload) {
                container.innerHTML = ImageUpload.render();
                Utils.fadeIn(container);
                
                // 컴포넌트 초기화
                ImageUpload.init();
                
                // 이전 데이터 복원
                const savedData = StateManager.loadImageData();
                if (savedData.images && savedData.images.length > 0) {
                    // 이미지 데이터가 있으면 복원 로직 실행
                    // ImageUpload 컴포넌트에서 복원 메서드가 있다면 호출
                    if (ImageUpload.restoreImages) {
                        ImageUpload.restoreImages(savedData.images);
                    }
                }
                
                // 이미지 업로드 이벤트 리스너 설정
                const fileInput = document.getElementById('image-upload');
                if (fileInput) {
                    fileInput.addEventListener('change', () => {
                        setTimeout(() => {
                            StateManager.saveImageData(ImageUpload.getData());
                            Navigation.setupNavigationButtons();
                        }, 100);
                    });
                }
                
                // 드래그 앤 드롭 영역 이벤트
                const dropZone = document.getElementById('drop-zone');
                if (dropZone) {
                    dropZone.addEventListener('drop', () => {
                        setTimeout(() => {
                            StateManager.saveImageData(ImageUpload.getData());
                            Navigation.setupNavigationButtons();
                        }, 100);
                    });
                }
                
                // 초기 네비게이션 상태 업데이트
                setTimeout(() => {
                    Navigation.setupNavigationButtons();
                }, 100);
            }
            

            // 브레드크럼 생성
            Navigation.createBreadcrumb();
        });
    </script>
</body>
</html>