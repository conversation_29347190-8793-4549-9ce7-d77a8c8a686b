<!DOCTYPE html>
<html lang="ko">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>4단계: 이미지 분석 - 신라 에어컨 콘텐츠 작성기</title>
    <link rel="stylesheet" href="styles/main.css">
    <link rel="stylesheet" href="styles/components.css">
</head>
<body class="step4-page">
    <div id="app">
        <header class="header">
            <h1><span class="brand-red">신라</span> <span class="brand-blue">에어컨</span> 콘텐츠 작성기</h1>
            <div class="progress-container">
                <div class="progress-bar" id="progress-bar"></div>
                <span class="progress-text" id="progress-text">67%</span>
            </div>
        </header>
        
        <main class="main-container">
            <!-- 브레드크럼 네비게이션 -->
            <nav class="breadcrumb-nav">
                <div id="breadcrumb"></div>
            </nav>
            
            <!-- 단계 컨테이너 -->
            <div id="step-container" class="step-container">
                <!-- 이미지 분석 컴포넌트가 여기에 로드됩니다 -->
            </div>
            
            <!-- 네비게이션 -->
            <div class="navigation">
                <button id="prev-btn" class="nav-btn">이전</button>
                <div class="step-indicator">
                    <div class="step-progress">
                        <span id="current-step">4</span> / <span id="total-steps">6</span>
                    </div>
                    <div class="step-name" id="step-name">이미지 분석</div>
                    <div class="step-dots">
                        <div class="step-dot completed"></div>
                        <div class="step-dot completed"></div>
                        <div class="step-dot completed"></div>
                        <div class="step-dot active"></div>
                        <div class="step-dot"></div>
                        <div class="step-dot"></div>
                    </div>
                </div>
                <button id="next-btn" class="nav-btn">다음</button>
            </div>
        </main>
    </div>
    
    <!-- 스크립트 로딩 -->
    <script src="js/utils.js"></script>
    <script src="js/stateManager.js"></script>
    <script src="js/navigation.js"></script>
    <!-- API 설정 및 텍스트 생성 모듈 -->
    <script src="js/config/apiConfig.js"></script>
    <script src="js/config/prompts.js"></script>
    <script src="js/api/textGenerationAPI.js"></script>
    <!-- OpenAI 이미지 편집 라이브러리 -->
    <script src="image-edit-api.js"></script>
    <!-- 연락처 이미지 생성 모듈 -->
    <script src="js/components/contactImageGenerator.js"></script>
    <script src="js/services/imageMapper.js"></script>
    <script src="js/components/step4DataDisplay.js"></script>
    
    <!-- 기본 컴포넌트만 유지 -->
    
    <script>
        // 페이지별 초기화
        document.addEventListener('DOMContentLoaded', () => {
            console.log('=== Step 4 페이지 로딩 시작 ===');
            
            // Session Storage 전체 상태 확인
            console.log('=== 현재 Session Storage 상태 ===');
            for (let i = 0; i < sessionStorage.length; i++) {
                const key = sessionStorage.key(i);
                const value = sessionStorage.getItem(key);
                console.log(`${key}:`, value);
            }
            
            // Session Storage에서 Step 3 데이터 불러오기
            let step3Data = null;
            try {
                const sessionData = sessionStorage.getItem('step3_complete_data');
                console.log('step3_complete_data 원본 데이터:', sessionData);
                
                if (sessionData) {
                    step3Data = JSON.parse(sessionData);
                    console.log('✅ Step 3 데이터를 Session Storage에서 불러왔습니다:', step3Data);
                    console.log('불러온 이미지 개수:', step3Data.images ? step3Data.images.length : 0);
                } else {
                    console.log('❌ step3_complete_data가 Session Storage에 없습니다.');
                }
            } catch (error) {
                console.error('❌ Session Storage 데이터 불러오기 오류:', error);
            }
            
            // 기본 컨테이너 설정
            const container = document.getElementById('step-container');
            container.innerHTML = '<div class="step4-content"><h2>4단계: 이미지 분석</h2><p>Session Storage에서 데이터를 불러오는 중...</p></div>';
            
            // Step4DataDisplay 모듈 초기화
            if (window.Step4DataDisplay) {
                Step4DataDisplay.init();
            } else {
                console.error('Step4DataDisplay 모듈을 찾을 수 없습니다.');
            }
                
            // Step 3 데이터를 화면에 표시 (날짜, 키워드 정보)
            if (step3Data) {
                displayStep3Data(step3Data);
            }
            
            // 초기 네비게이션 상태 업데이트
            setTimeout(() => {
                Navigation.setupNavigationButtons();
            }, 100);
            
            // 브레드크럼 생성
            Navigation.createBreadcrumb();
        });
        
        // Step 3 데이터를 화면에 표시하는 함수
        function displayStep3Data(step3Data) {
            // 데이터 표시 영역이 있는지 확인하고 생성
            let dataDisplayArea = document.getElementById('step3-data-display');
            if (!dataDisplayArea) {
                // 데이터 표시 영역을 step-container 상단에 추가
                const stepContainer = document.getElementById('step-container');
                dataDisplayArea = document.createElement('div');
                dataDisplayArea.id = 'step3-data-display';
                dataDisplayArea.className = 'previous-data-container';
                stepContainer.insertBefore(dataDisplayArea, stepContainer.firstChild);
            }
            
            let infoCards = '';
            
            // 날짜 정보 카드
            if (step3Data.dateInfo) {
                let dateText = '';
                if (step3Data.dateInfo.isUnspecified) {
                    dateText = '날짜 모름';
                } else if (step3Data.dateInfo.selectedDate) {
                    const date = new Date(step3Data.dateInfo.selectedDate);
                    const options = { year: 'numeric', month: 'long', day: 'numeric', weekday: 'long' };
                    dateText = date.toLocaleDateString('ko-KR', options);
                }
                
                if (dateText) {
                    infoCards += `
                        <div class="info-card date-card">
                            <h4>📅 작업 날짜</h4>
                            <p>${dateText}</p>
                        </div>
                    `;
                }
            }
            
            // 키워드 정보 카드
            if (step3Data.keywordInfo) {
                let hasKeywordData = false;
                let keywordContent = '';
                
                if (step3Data.keywordInfo.modelName && step3Data.keywordInfo.modelName.trim()) {
                    keywordContent += `<div class="info-item"><strong>🏷️ 모델명:</strong> ${step3Data.keywordInfo.modelName.trim()}</div>`;
                    hasKeywordData = true;
                }
                
                if (step3Data.keywordInfo.location && step3Data.keywordInfo.location.trim()) {
                    keywordContent += `<div class="info-item"><strong>📍 지역:</strong> ${step3Data.keywordInfo.location.trim()}</div>`;
                    hasKeywordData = true;
                }
                
                if (step3Data.keywordInfo.additionalPoints && step3Data.keywordInfo.additionalPoints.trim()) {
                    keywordContent += `<div class="info-item"><strong>💡 추가 포인트:</strong> ${step3Data.keywordInfo.additionalPoints.trim()}</div>`;
                    hasKeywordData = true;
                }
                
                if (hasKeywordData) {
                    infoCards += `
                        <div class="info-card keyword-card">
                            <h4>🔧 키워드 정보</h4>
                            <div class="keyword-content">${keywordContent}</div>
                        </div>
                    `;
                }
            }
            
            // 이미지 정보 카드
            if (step3Data.images && step3Data.images.length > 0) {
                let imageInfo = `<div class="info-item"><strong>📷 업로드된 이미지:</strong> ${step3Data.images.length}개</div>`;
                
                // 분석된 이미지 개수 확인
                const analyzedImages = step3Data.images.filter(img => img.analysis && img.analysis.success);
                if (analyzedImages.length > 0) {
                    imageInfo += `<div class="info-item"><strong>🔍 분석 완료:</strong> ${analyzedImages.length}개</div>`;
                }
                
                infoCards += `
                    <div class="info-card image-card">
                        <h4>🖼️ 이미지 정보</h4>
                        <div class="image-content">${imageInfo}</div>
                    </div>
                `;
            }
            
            if (infoCards) {
                dataDisplayArea.innerHTML = `
                    <h3>📋 이전 단계 정보</h3>
                    <div class="info-cards-grid">
                        ${infoCards}
                    </div>
                `;
                dataDisplayArea.style.marginBottom = '30px';
            }
        }
    </script>
</body>
</html>