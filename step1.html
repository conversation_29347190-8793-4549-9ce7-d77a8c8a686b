<!DOCTYPE html>
<html lang="ko">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>1단계: 날짜 선택 - 신라 에어컨 콘텐츠 작성기</title>
    <link rel="stylesheet" href="styles/main.css">
    <link rel="stylesheet" href="styles/components.css">
</head>
<body>
    <div id="app">
        <header class="header">
            <h1><span class="brand-red">신라</span> <span class="brand-blue">에어컨</span> 콘텐츠 작성기</h1>
            <div class="progress-container">
                <div class="progress-bar" id="progress-bar"></div>
                <span class="progress-text" id="progress-text">17%</span>
            </div>
        </header>
        
        <main class="main-container">
            <!-- 브레드크럼 네비게이션 -->
            <nav class="breadcrumb-nav">
                <div id="breadcrumb"></div>
            </nav>
            
            <!-- 단계 컨테이너 -->
            <div id="step-container" class="step-container">
                <!-- 날짜 선택 컴포넌트가 여기에 로드됩니다 -->
            </div>
            
            <!-- 네비게이션 -->
            <div class="navigation">
                <button id="prev-btn" class="nav-btn" disabled>이전</button>
                <div class="step-indicator">
                    <div class="step-progress">
                        <span id="current-step">1</span> / <span id="total-steps">4</span>
                    </div>
                    <div class="step-name" id="step-name">날짜 선택</div>
                    <div class="step-dots">
                        <div class="step-dot active"></div>
                        <div class="step-dot"></div>
                        <div class="step-dot"></div>
                        <div class="step-dot"></div>
                    </div>
                </div>
                <button id="next-btn" class="nav-btn">다음</button>
            </div>
        </main>
    </div>
    
    <!-- 스크립트 로딩 -->
    <script src="js/utils.js"></script>
    <script src="js/stateManager.js"></script>
    <script src="js/navigation.js"></script>
    <script src="js/components/dateSelector.js"></script>
    
    <script>
        // 페이지별 초기화
        document.addEventListener('DOMContentLoaded', () => {
            // 날짜 선택 컴포넌트 로드
            const container = document.getElementById('step-container');
            if (DateSelector) {
                container.innerHTML = DateSelector.render();
                Utils.fadeIn(container);
                
                // 컴포넌트 초기화
                DateSelector.init();
                
                // 이전 데이터 복원
                const savedData = StateManager.loadDateData();
                if (savedData.selectedDate) {
                    const dateInput = document.getElementById('selected-date');
                    if (dateInput) dateInput.value = savedData.selectedDate;
                }
                if (savedData.isUnspecified) {
                    const checkbox = document.getElementById('date-unspecified');
                    if (checkbox) checkbox.checked = true;
                }
                
                // 실시간 데이터 저장
                const dateInput = document.getElementById('selected-date');
                const checkbox = document.getElementById('date-unspecified');
                
                if (dateInput) {
                    dateInput.addEventListener('change', () => {
                        StateManager.saveDateData(DateSelector.getData());
                        Navigation.setupNavigationButtons();
                    });
                }
                
                if (checkbox) {
                    checkbox.addEventListener('change', () => {
                        StateManager.saveDateData(DateSelector.getData());
                        Navigation.setupNavigationButtons();
                    });
                }
            }
            
            // 브레드크럼 생성
            Navigation.createBreadcrumb();
        });
    </script>
</body>
</html>