const http = require('http');
const https = require('https');
const url = require('url');

const server = http.createServer((req, res) => {
    // CORS 헤더 설정
    res.setHeader('Access-Control-Allow-Origin', '*');
    res.setHeader('Access-Control-Allow-Methods', 'GET, POST, OPTIONS');
    res.setHeader('Access-Control-Allow-Headers', 'Content-Type');

    // OPTIONS 요청 처리 (preflight)
    if (req.method === 'OPTIONS') {
        res.writeHead(200);
        res.end();
        return;
    }

    if (req.method !== 'GET') {
        res.writeHead(405, { 'Content-Type': 'application/json' });
        res.end(JSON.stringify({ error: 'Method not allowed' }));
        return;
    }

    const parsedUrl = url.parse(req.url, true);
    const imageUrl = parsedUrl.query.url;

    if (!imageUrl) {
        res.writeHead(400, { 'Content-Type': 'application/json' });
        res.end(JSON.stringify({ error: 'URL parameter is required' }));
        return;
    }

    // OpenAI URL인지 확인 (보안을 위해)
    if (!imageUrl.includes('oaidalleapiprodscus.blob.core.windows.net')) {
        res.writeHead(403, { 'Content-Type': 'application/json' });
        res.end(JSON.stringify({ error: 'Only OpenAI image URLs are allowed' }));
        return;
    }

    console.log('프록시 요청:', imageUrl);

    // HTTPS 요청으로 이미지 가져오기
    const imageRequest = https.get(imageUrl, (imageRes) => {
        if (imageRes.statusCode !== 200) {
            res.writeHead(500, { 'Content-Type': 'application/json' });
            res.end(JSON.stringify({ error: 'Failed to fetch image' }));
            return;
        }

        // 이미지 헤더 복사
        res.setHeader('Content-Type', imageRes.headers['content-type'] || 'image/png');
        res.setHeader('Content-Length', imageRes.headers['content-length']);
        res.setHeader('Cache-Control', 'public, max-age=3600');

        res.writeHead(200);
        
        // 이미지 데이터 스트리밍
        imageRes.pipe(res);
    });

    imageRequest.on('error', (error) => {
        console.error('이미지 요청 오류:', error);
        res.writeHead(500, { 'Content-Type': 'application/json' });
        res.end(JSON.stringify({ error: 'Failed to fetch image' }));
    });

    imageRequest.setTimeout(30000, () => {
        imageRequest.destroy();
        res.writeHead(500, { 'Content-Type': 'application/json' });
        res.end(JSON.stringify({ error: 'Request timeout' }));
    });
});

const PORT = 8080;
server.listen(PORT, () => {
    console.log(`프록시 서버가 http://localhost:${PORT} 에서 실행 중입니다.`);
});

server.on('error', (error) => {
    console.error('서버 오류:', error);
});